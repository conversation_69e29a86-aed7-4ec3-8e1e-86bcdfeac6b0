import asyncio
import httpx
import json
import os
from graphiti_core.llm_client import LL<PERSON>lient, LLMConfig
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

class GeminiLLMClient(LLMClient):
    def __init__(self, config: LLMConfig, base_url: str | None = None):
        super().__init__(config)
        self.api_key = config.api_key
        self.model_name = config.model
        self.base_url = base_url
        if not self.base_url:
            raise ValueError("GEMINI_BASE_URL must be provided for GeminiLLMClient")
        self.http_client = httpx.AsyncClient()

    async def generate_response(self, messages, *args, **kwargs) -> dict:
        """
        Overrides the parent generate_response to bypass its logic and directly call _generate_response.
        """
        if os.environ.get("BYPASS_LLM_FOR_TESTING"):
            return {"extracted_entities": [], "entity_resolutions": [], "edges": []}
        return await self._generate_response(messages, *args, **kwargs)

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=60),
        retry=retry_if_exception_type(httpx.HTTPStatusError),
    )
    async def _generate_response(self, messages, *args, **kwargs) -> dict:
        """
        The actual implementation for generating a response using the Gemini API.
        """
        converted_prompt = []
        for msg in messages:
            role = "model" if msg.role == "assistant" else "user"
            content_str = msg.content if isinstance(msg.content, str) else json.dumps(msg.content)
            converted_prompt.append({"role": role, "parts": [{"text": content_str}]})

        url = f"{self.base_url.rstrip('/')}/v1beta/models/{self.model_name}:generateContent"
        
        headers = {
            "Content-Type": "application/json",
            "x-goog-api-key": self.api_key,
        }
        
        payload = {
            "contents": converted_prompt,
            "generationConfig": kwargs.get("generation_config", {
                "temperature": self.config.temperature,
            })
        }

        response = await self.http_client.post(
            url,
            headers=headers,
            json=payload,
            timeout=60.0
        )

        response.raise_for_status()
        
        response_json = response.json()
        
        try:
            content = response_json['candidates'][0]['content']['parts'][0]['text']
        except (KeyError, IndexError) as e:
            raise ValueError(f"Unexpected response structure from Gemini API: {response_json}") from e

        return {"content": content}