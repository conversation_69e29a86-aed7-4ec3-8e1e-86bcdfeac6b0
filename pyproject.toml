[project]
name = "mcp-server"
version = "0.4.0"
description = "Graphiti MCP Server"
readme = "README.md"
requires-python = ">=3.10,<4"
dependencies = [
    "mcp>=1.5.0",
    "openai>=1.68.2",
    "graphiti-core>=0.14.0",
    "azure-identity>=1.21.0",
    "google-generativeai>=0.5.0",
    "tenacity>=8.2.3",
    "fastapi>=0.111.0",
    "uvicorn>=0.29.0",
]

[project.scripts]
graphiti-mcp-server = "graphiti_mcp_server:main"

[tool.setuptools]
py-modules = ["graphiti_mcp_server", "gemini_llm_client"]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-asyncio",
]