import google.generativeai as genai
from google.api_core import exceptions as google_exceptions
from graphiti_core.llm_client import LL<PERSON>lient, LLMConfig
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

class GeminiLLMClient(LLMClient):
    def __init__(self, config: LLMConfig, base_url: str | None = None):
        super().__init__(config)
        client_options = {"api_endpoint": base_url} if base_url else None
        genai.configure(api_key=config.api_key, client_options=client_options)
        self.model = genai.GenerativeModel(config.model)

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=60),
        stop=stop_after_attempt(3),
        retry=retry_if_exception_type(
            (
                google_exceptions.ResourceExhausted,  # HTTP 429
                google_exceptions.ServiceUnavailable, # HTTP 503
            )
        ),
    )
    async def _generate_response(self, *args, **kwargs) -> str:
        prompt = args[0] if args else ""
        response = await self.model.generate_content_async(prompt, **kwargs)
        return response.text