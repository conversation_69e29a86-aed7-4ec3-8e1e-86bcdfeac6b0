import sys
import json
import uuid
import requests
import time
import os
from dotenv import load_dotenv

# The server URL for SSE testing
SERVER_URL = "http://10.11.5.202:8002/"

def send_request(method, params, request_id):
    """Helper function to send a JSON-RPC request via HTTP POST and get the response."""
    request = {
        "jsonrpc": "2.0",
        "method": method,
        "params": params,
        "id": request_id
    }
    headers = {'Content-Type': 'application/json'}
    
    try:
        print(f"Sending request for {method}...", file=sys.stderr)
        response = requests.post(SERVER_URL, json=request, headers=headers, timeout=60)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        
        response_json = response.json()
        print(f"Received for {method}: {json.dumps(response_json, indent=2)}", file=sys.stderr)
        return response_json

    except requests.exceptions.RequestException as e:
        print(f'\n--- Test failed for {method}: HTTP Request failed: {e} ---', file=sys.stderr)
        return None
    except json.JSONDecodeError:
        print(f'\n--- Test failed for {method}: Failed to decode JSON response: {response.text} ---', file=sys.stderr)
        return None


def main():
    """
    Test script for the Graphiti MCP server focusing on search, get, and delete operations.
    """
    # Load environment variables from .env file
    load_dotenv()
    
    # --- 1. Initial clear_graph to ensure a clean slate ---
    print('\n--- 1. Testing clear_graph (initial) ---', file=sys.stderr)
    clear_response = send_request('clear_graph', {}, 'clear_1')
    assert clear_response and 'result' in clear_response, "Initial clear_graph failed"
    print('--- clear_graph (initial) successful ---', file=sys.stderr)

    # --- 2. Add memory episodes ---
    print('\n--- 2. Testing add_memory ---', file=sys.stderr)
    add_text_response = send_request(
        'add_memory',
        {'name': 'Test Search Episode', 'episode_body': 'This is a test for search and delete.', 'source': 'text'},
        'add_text_1'
    )
    assert add_text_response and 'result' in add_text_response and 'error' not in add_text_response['result'], f"add_memory (text) failed: {add_text_response.get('result', {}).get('error')}"
    
    # Add a delay to allow the server to process the queued episodes
    print('\n--- Waiting 15 seconds for episode processing... ---', file=sys.stderr)
    time.sleep(15)

    # --- 3. Use get_all_nodes_and_facts to retrieve created items ---
    print('\n--- 3. Testing get_all_nodes_and_facts ---', file=sys.stderr)
    get_all_response = send_request('get_all_nodes_and_facts', {}, 'get_all_1')
    assert get_all_response and 'result' in get_all_response, "get_all_nodes_and_facts failed"
    
    nodes = get_all_response['result'].get('nodes', [])
    facts = get_all_response['result'].get('facts', [])

    print(f"--- Retrieved {len(nodes)} nodes and {len(facts)} facts ---", file=sys.stderr)
    
    assert len(nodes) > 0, "Should have retrieved at least one node."
    # We expect at least one episode node.
    episode_node = next((n for n in nodes if n['name'] == 'Test Search Episode'), None)
    assert episode_node is not None, "Test Search Episode not found"
    episode_to_delete_uuid = episode_node['uuid']

    # --- 4. Test search (expecting no results with NoOpEmbedder) ---
    print('\n--- 4. Testing search_memory_nodes and search_memory_facts ---', file=sys.stderr)
    search_nodes_response = send_request('search_memory_nodes', {'query': 'test'}, 'search_nodes_1')
    assert search_nodes_response and 'result' in search_nodes_response, "search_memory_nodes failed"
    assert 'Vector search not available' in search_nodes_response['result']['message'], "Message should indicate vector search is unavailable"

    search_facts_response = send_request('search_memory_facts', {'query': 'test'}, 'search_facts_1')
    assert search_facts_response and 'result' in search_facts_response, "search_memory_facts failed"
    assert 'Vector search not available' in search_facts_response['result']['message'], "Message should indicate vector search is unavailable"
    print('--- Search tests successful (as expected for NoOpEmbedder) ---', file=sys.stderr)

    # --- 5. Test get and delete operations ---
    if facts:
        edge_to_get_uuid = facts[0]['uuid']
        print(f"\n--- 5a. Testing get_entity_edge with UUID: {edge_to_get_uuid} ---", file=sys.stderr)
        get_edge_response = send_request('get_entity_edge', {'uuid': edge_to_get_uuid}, 'get_edge_1')
        assert get_edge_response and 'result' in get_edge_response, "get_entity_edge failed"
        assert get_edge_response['result']['uuid'] == edge_to_get_uuid, "get_entity_edge returned wrong edge"
        print('--- get_entity_edge test successful ---', file=sys.stderr)

        print(f"\n--- 5b. Testing delete_entity_edge with UUID: {edge_to_get_uuid} ---", file=sys.stderr)
        delete_edge_response = send_request('delete_entity_edge', {'uuid': edge_to_get_uuid}, 'delete_edge_1')
        assert delete_edge_response and 'result' in delete_edge_response, "delete_entity_edge failed"
        print('--- delete_entity_edge test successful ---', file=sys.stderr)
    else:
        print("\n--- Skipping get/delete edge tests because no facts were created ---", file=sys.stderr)

    print(f"\n--- 5c. Testing delete_episode with UUID: {episode_to_delete_uuid} ---", file=sys.stderr)
    delete_episode_response = send_request('delete_episode', {'uuid': episode_to_delete_uuid}, 'delete_episode_1')
    assert delete_episode_response and 'result' in delete_episode_response, "delete_episode failed"
    print('--- delete_episode test successful ---', file=sys.stderr)

    # --- 6. Final clear_graph to clean up ---
    print('\n--- 6. Testing clear_graph (final) ---', file=sys.stderr)
    final_clear_response = send_request('clear_graph', {}, 'clear_final')
    assert final_clear_response and 'result' in final_clear_response, "Final clear_graph failed"
    print('--- clear_graph (final) successful ---', file=sys.stderr)

    print('\n\n--- ALL SEARCH AND DELETE TESTS PASSED SUCCESSFULLY ---', file=sys.stderr)

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f'\n--- A test failed with an unhandled exception: {e} ---', file=sys.stderr)
        sys.exit(1)