import asyncio
import os
from datetime import datetime, timezone
os.environ["BYPASS_LLM_FOR_TESTING"] = "true"
import httpx
from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
import graphiti_mcp_server
from graphiti_mcp_server import (
    GraphitiConfig,
    generate_embeddings,
    NoOpEmbedder,
    VectorServiceEmbedder,
    VectorServiceConfig,
    rebuild_indices,
    regenerate_all_node_vectors,
    clear_graph,
    initialize_graphiti,
    add_memory,
    search_memory_nodes,
)
from gemini_llm_client import GeminiLLMClient
from graphiti_core.llm_client.config import LLMConfig

# Load environment variables
load_dotenv()

async def run_test():
    """
    A clean, end-to-end test for adding data and verifying automatic vector generation.
    """
    print("--- Starting E2E Test: Automatic Vector Generation on Addition ---")

    # Use a single client for all HTTP requests
    async with httpx.AsyncClient() as client:
        # 1. Initialize Graphiti using the server's logic
        graphiti_config = GraphitiConfig.from_env()
        graphiti_config.neo4j.uri = 'bolt://10.11.5.202:7687'
        graphiti_config.neo4j.password = '111QQQqqq111'
        graphiti_mcp_server.config = graphiti_config
        await initialize_graphiti(graphiti_config)
        print("Graphiti client initialized.")

        # 2. Clean Slate
        print("\n--- Clearing graph and rebuilding indices ---")
        await clear_graph()
        await rebuild_indices()
        print("Graph cleared and indices rebuilt.")

        # 3. Add a test episode and verify vectors are created
        test_name = "Episode with Auto-Generated Vectors"
        test_content = "This content should trigger automatic vector creation."
        test_group_id = "auto_vector_test"
        
        print(f"\n--- Adding episode: '{test_name}' ---")
        add_result = await add_memory(
            name=test_name,
            episode_body=test_content,
            group_id=test_group_id,
        )
        if 'error' in add_result:
            print(f"FAILURE: Could not add episode: {add_result['error']}")
            return
        print("Episode queued. Waiting for processing and vector generation...")
        # Wait for the episode to be processed by the background worker, including vector generation
        await asyncio.sleep(15) # Increased sleep time to ensure vector generation completes

        # 4. Verify the results
        print("\n--- Verifying results ---")
        search_query = "content"
        search_result = await search_memory_nodes(query=search_query, group_ids=[test_group_id])

        if 'error' in search_result:
            print(f"FAILURE: Search failed: {search_result['error']}")
            return

        nodes = search_result.get('nodes', [])
        if not nodes:
            print("FAILURE: Search did not return any results.")
            return

        print("SUCCESS: Search returned results. Now verifying node details...")
        all_nodes_have_vectors = True
        for node in nodes:
            print(f"  - Checking node: '{node['name']}' (UUID: {node['uuid']})")
            # Use a local driver session to check the node directly
            try:
                async with graphiti_mcp_server.graphiti_client.driver.session() as session:
                    result = await session.run("MATCH (n {uuid: $uuid}) RETURN n", uuid=node['uuid'])
                    record = await result.single()
                    if record:
                        node_details = record['n']
                        has_dense = 'dense_vector' in node_details and node_details['dense_vector'] is not None
                        has_sparse = 'sparse_vector_indices' in node_details and node_details['sparse_vector_indices'] is not None
                        print(f"    - Dense vector exists: {has_dense}")
                        print(f"    - Sparse vector exists: {has_sparse}")
                        if not has_dense or not has_sparse:
                            all_nodes_have_vectors = False
                    else:
                        print(f"    - FAILURE: Could not find node with UUID {node['uuid']} in database.")
                        all_nodes_have_vectors = False
            except Exception as e:
                print(f"    - FAILURE: An error occurred while verifying node {node['uuid']}: {e}")
                all_nodes_have_vectors = False

        if all_nodes_have_vectors:
            print("\n--- VERIFICATION SUCCESSFUL: All retrieved nodes have vectors. ---")
        else:
            print("\n--- VERIFICATION FAILED: Some nodes are missing vectors. ---")

    print("\n--- Test Finished ---")


if __name__ == "__main__":
    asyncio.run(run_test())