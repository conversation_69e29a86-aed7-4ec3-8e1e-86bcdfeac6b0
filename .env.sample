# Graphiti MCP Server Environment Configuration

# Neo4j Database Configuration
# These settings are used to connect to your Neo4j database
#NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=demodemo

# --- LLM Provider Configuration ---
# Choose your LLM provider: "openai", "azure", or "gemini"
# LLM_PROVIDER=openai

# --- Google Gemini Configuration ---
# Required if LLM_PROVIDER is "gemini"
# GOOGLE_API_KEY=your_google_api_key_here
# GEMINI_MODEL=gemini-1.5-flash-latest
# Optional: Custom Gemini endpoint
# GEMINI_BASE_URL=your_gemini_base_url_here

# --- OpenAI API Configuration ---
# Required if LLM_PROVIDER is "openai"
OPENAI_API_KEY=your_openai_api_key_here
MODEL_NAME=gpt-4.1-mini


# Optional: Only needed for non-standard OpenAI endpoints
# OPENAI_BASE_URL=https://api.openai.com/v1

# Optional: Group ID for namespacing graph data
# GROUP_ID=my_project

# Optional: Path configuration for Docker
# PATH=/root/.local/bin:${PATH}

# Optional: Memory settings for Neo4j (used in Docker Compose)
# NEO4J_server_memory_heap_initial__size=512m
# NEO4J_server_memory_heap_max__size=1G
# NEO4J_server_memory_pagecache_size=512m

# Azure OpenAI configuration
# Optional: Only needed for Azure OpenAI endpoints
# AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here
# AZURE_OPENAI_API_VERSION=2025-01-01-preview
# AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o-gpt-4o-mini-deployment
# AZURE_OPENAI_EMBEDDING_API_VERSION=2023-05-15
# AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME=text-embedding-3-large-deployment
# AZURE_OPENAI_USE_MANAGED_IDENTITY=false
# ----------------------------------
# Vector Manufacturing Machine Config
# ----------------------------------
VECTOR_SERVICE_ENDPOINT="http://10.11.5.201:8002/api/v1/embeddings"
VECTOR_SERVICE_MODEL="bge-m3"
# A comma-separated list of embedding types to generate. Supported types: "dense", "sparse"
VECTOR_SERVICE_EMBEDDING_TYPES=dense,sparse
# The dimension of the vector embeddings. This must match the model being used.
# For BAAI/bge-m3, this is 1024. For text-embedding-ada-002, it's 1536.
VECTOR_DIMENSION=1024