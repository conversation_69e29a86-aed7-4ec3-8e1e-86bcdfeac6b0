# Graphiti MCP Server 專案概觀

## 1. 核心目的

Graphiti MCP Server 是一個為 AI 代理（AI Agents）設計的**知識圖譜記憶體服務**。它的核心功能是將非結構化或結構化的資訊（如對話、文件、JSON 數據）轉換為一個動態的、可查詢的知識網路，並透過模型上下文協定（Model Context Protocol, MCP）提供給 AI 代理使用。

簡單來說，它扮演著 AI 代理的「外接大腦」，使其能夠記憶、關聯和檢索複雜的資訊。

## 2. 主要技術棧與組件

- **核心框架**: `graphiti-core` - 提供了與知識圖譜互動的基礎能力。
- **資料庫**: `Neo4j` - 作為底層的圖形資料庫，用於儲存節點（實體）和邊（關係）。
- **通訊協定**: `mcp` - 實現了與 AI 代理客戶端（如 Cursor IDE）的標準化通訊。伺服器端使用 `FastMCP` 框架，支援 SSE 和 Stdio 傳輸。
- **大型語言模型 (LLM)**:
    - **OpenAI**: 預設使用 `gpt-4.1-mini` 等模型。
    - **Azure OpenAI**: 完整支援 Azure 的端點和身份驗證。
    - **Google Gemini**: 也整合了 Gemini 模型作為選項。
- **嵌入模型 (Embedder)**: 使用 `text-embedding-3-small` 等模型將文字轉換為向量，用於語意搜尋。
- **部署**: `Docker` 和 `docker-compose` - 提供了一鍵式的部署和管理方式。

## 3. 架構與工作流程

專案的架構可以總結如下：

```mermaid
graph TD
    subgraph "AI Agent / Client"
        A[MCP Client e.g., Cursor IDE]
    end

    subgraph "Graphiti MCP Server (This Project)"
        B(MCP Server Interface)
        C{Configuration Management}
        D[Async Task Queue]
        E[Graphiti Core Client]
        F[LLM Client Abstraction]
    end

    subgraph "External Services"
        G[Neo4j Database]
        H[OpenAI / Azure / Gemini API]
    end

    A -- MCP Request (e.g., add_memory) --> B
    B -- Parses Request --> D
    D -- Enqueues Task --> E
    E -- Processes Task --> G
    E -- Needs LLM for Entity Extraction --> F
    F -- Selects Provider --> H
    H -- Returns LLM/Embedding Result --> F
    F -- Returns to --> E
    E -- Writes to --> G

    C -- Loads from .env & CLI --> B
    C -- Loads from .env & CLI --> F
    C -- Loads from .g & CLI --> E
```

**工作流程範例 (新增記憶):**

1.  **客戶端請求**: 一個 MCP 客戶端（如 Cursor）發送一個 `add_memory` 請求到 Graphiti MCP Server。
2.  **接收與排隊**: 伺服器的 `FastMCP` 介面接收到請求。為了避免資料庫寫入衝突，它不會立即處理，而是將該任務打包成一個非同步函式，放入一個與 `group_id` 對應的 `asyncio.Queue` 中。
3.  **背景處理**: 一個專門的背景 worker 從佇列中取出任務並執行。
4.  **核心處理**: `Graphiti` 核心客戶端接收到資料。
5.  **實體提取 (可選)**: 如果啟用了 `--use-custom-entities`，`Graphiti` 會呼叫 LLM Client（如 OpenAI 或 Gemini）來分析文本，並根據預定義的 Pydantic 模型（`Requirement`, `Preference` 等）提取結構化實體。
6.  **向量化**: 文字內容和提取出的「事實」（Facts）會被送往 Embedder Client 進行向量化。
7.  **寫入資料庫**: `Graphiti` 將 episode、實體節點、關係邊以及它們的向量嵌入寫入 Neo4j 資料庫。

## 4. 核心功能 (MCP Tools)

- `add_memory`: 新增一筆資訊（稱為 episode），可以是文字、對話或 JSON。這是將知識注入系統的主要入口。
- `search_memory_nodes`: 根據自然語言查詢，在知識圖譜中進行語意搜尋，找出最相關的實體（節點）。
- `search_memory_facts`: 搜尋最相關的事實（節點之間的關係/邊）。
- `get_episodes` / `get_entity_edge`: 根據 UUID 直接獲取特定的 episode 或事實。
- `delete_episode` / `delete_entity_edge`: 刪除指定的資料。
- `clear_graph`: 清空整個知識圖譜。

## 5. 組態與彈性

專案的設計具有高度的彈性：

- **LLM 可插拔**: 可以輕易地在 OpenAI、Azure 和 Gemini 之間切換。
- **環境變數驅動**: 所有關鍵設定（API Keys, Endpoints, Model Names）都可透過 `.env` 檔案或環境變數進行配置，無需修改程式碼。
- **命令列覆寫**: 提供了命令列參數，可以在啟動時覆寫環境變數的設定。
- **自訂實體**: 允許開發者定義自己的 Pydantic 模型，以從文本中提取特定領域的結構化資訊。

## 6. 總結

Graphiti MCP Server 不僅僅是一個簡單的 API 伺服器，它是一個複雜但設計良好的系統，旨在解決 AI 代理的長期記憶問題。它巧妙地結合了知識圖譜的結構化優勢和 LLM 的自然語言處理能力，並透過標準化的 MCP 協定，為下一代 AI 應用提供了強大的記憶體基礎設施。