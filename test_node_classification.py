import asyncio
import pytest
from typing import cast
import uuid

from graphiti_mcp_server import (
    add_memory,
    search_memory_nodes,
    initialize_server,
    initialize_graphiti,
    clear_graph,
    graphiti_client,
    config,
)

# Mark all tests in this module as asyncio
pytestmark = pytest.mark.asyncio


@pytest.fixture(scope="module", autouse=True)
async def setup_and_teardown_module():
    """Module-level setup and teardown."""
    # Setup: Initialize server and graphiti client
    cli_args = ["--transport", "sse"]  # Use SSE for testing purposes
    await initialize_server(cli_args)
    await initialize_graphiti(config)

    # Clear the graph before running tests
    await clear_graph()

    yield

    # Teardown: Not strictly necessary to clear after, but good practice
    await clear_graph()
    if graphiti_client:
        await graphiti_client.close()


async def wait_for_episode_processing(group_id: str):
    """Wait for the episode queue for a specific group_id to be empty."""
    from graphiti_mcp_server import episode_queues
    if group_id in episode_queues:
        await episode_queues[group_id].join()


@pytest.fixture(autouse=True)
async def clear_graph_before_each_test():
    """Clear the graph before each test function."""
    await clear_graph()


async def test_add_memory_with_labels():
    """Test adding a memory with labels and verify the node has the correct labels."""
    group_id = f"test-labels-{uuid.uuid4()}"
    episode_name = "Test Architecture Episode"
    labels_to_add = ["Architecture", "Database"]

    # Add memory with labels
    add_result = await add_memory(
        name=episode_name,
        episode_body="This is a test about database architecture.",
        group_id=group_id,
        labels=labels_to_add,
    )
    assert "error" not in add_result, f"add_memory returned an error: {add_result.get('error')}"

    # Wait for background processing to complete
    await wait_for_episode_processing(group_id)

    # Verify the node was created with the correct labels
    client = cast(type(graphiti_client), graphiti_client)
    async with client.driver.session() as session:
        result = await session.run(
            "MATCH (n:EpisodicNode {name: $name, group_id: $group_id}) RETURN n.labels AS labels",
            name=episode_name,
            group_id=group_id,
        )
        record = await result.single()
        assert record is not None, "Node was not created."
        
        # The 'labels' property on the node should contain the custom labels
        # Note: Neo4j labels are different from properties. The EpisodicNode model stores them in a 'labels' property.
        stored_labels = record["labels"]
        assert all(label in stored_labels for label in labels_to_add)


async def test_search_nodes_with_single_label():
    """Test searching for nodes using a single label."""
    group_id = f"test-single-label-search-{uuid.uuid4()}"
    arch_episode_name = "Architecture Node"
    bug_episode_name = "Bug Solution Node"

    # Add an architecture node
    await add_memory(
        name=arch_episode_name,
        episode_body="This is an architecture description.",
        group_id=group_id,
        labels=["Architecture"],
    )
    # Add a bug solution node
    await add_memory(
        name=bug_episode_name,
        episode_body="This is a bug solution.",
        group_id=group_id,
        labels=["BugSolution"],
    )

    await wait_for_episode_processing(group_id)

    # Search for architecture nodes
    search_result = await search_memory_nodes(
        query="architecture", group_ids=[group_id], labels=["Architecture"]
    )

    assert "error" not in search_result, f"search_memory_nodes returned an error: {search_result.get('error')}"
    assert len(search_result["nodes"]) == 1
    assert search_result["nodes"][0]["name"] == arch_episode_name


async def test_search_nodes_with_multiple_labels():
    """Test searching for nodes using multiple labels."""
    group_id = f"test-multi-label-search-{uuid.uuid4()}"
    
    # Add nodes with different combinations of labels
    await add_memory(name="Arch-DB", episode_body="...", group_id=group_id, labels=["Architecture", "Database"])
    await add_memory(name="Arch-API", episode_body="...", group_id=group_id, labels=["Architecture", "API"])
    await add_memory(name="Bug-DB", episode_body="...", group_id=group_id, labels=["BugSolution", "Database"])

    await wait_for_episode_processing(group_id)

    # Search for nodes with both "Architecture" and "Database" labels
    search_result = await search_memory_nodes(
        query="database", group_ids=[group_id], labels=["Architecture", "Database"]
    )
    
    assert "error" not in search_result
    assert len(search_result["nodes"]) == 1
    assert search_result["nodes"][0]["name"] == "Arch-DB"

async def test_search_nodes_with_nonexistent_label():
    """Test that searching with a label that has no nodes returns an empty list."""
    group_id = f"test-nonexistent-label-search-{uuid.uuid4()}"
    await add_memory(name="Some Node", episode_body="...", group_id=group_id, labels=["Architecture"])
    
    await wait_for_episode_processing(group_id)

    # Search for a label that doesn't exist on any node
    search_result = await search_memory_nodes(
        query="any", group_ids=[group_id], labels=["NonExistentLabel"]
    )

    assert "error" not in search_result
    assert len(search_result["nodes"]) == 0