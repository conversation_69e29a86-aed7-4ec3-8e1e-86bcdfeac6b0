#!/usr/bin/env python3
"""
Graphiti MCP Server - Exposes Graphiti functionality through the Model Context Protocol (MCP)
"""

import argparse
import asyncio
import logging
import os
import sys
import uuid as uuid_lib
from collections.abc import Callable
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Any, cast, List, Union
from typing_extensions import TypedDict

import httpx

# A simple class to represent a message, satisfying the graphiti-core client's expectation for an object with attributes.
class Message:
    """A message in a conversation."""
    def __init__(self, role: str, content: str):
        self.role = role
        self.content = content

from azure.identity import DefaultAzureCredential, get_bearer_token_provider
from dotenv import load_dotenv
import json
from fastapi import Request, Response
from mcp.server.fastmcp import FastMCP
import uvicorn
from openai import AsyncAzureOpenAI
from pydantic import BaseModel, Field

from gemini_llm_client import GeminiLLMClient
from graphiti_core import Graphiti
from graphiti_core.edges import EntityEdge
from graphiti_core.embedder.azure_openai import AzureOpenAIEmbedderClient
from graphiti_core.embedder.client import EmbedderClient
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.llm_client import LLMClient
from graphiti_core.llm_client.azure_openai_client import AzureOpenAILLMClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.llm_client.openai_client import OpenAIClient
# This change was already applied in the previous step.
# I am re-applying it to ensure consistency.
from graphiti_core.nodes import EpisodeType, EpisodicNode, Node
from pydantic import Field
from typing import List, Optional

from graphiti_core.search.search_config_recipes import (
    NODE_HYBRID_SEARCH_NODE_DISTANCE,
    NODE_HYBRID_SEARCH_RRF,
)
from graphiti_core.search.search_filters import SearchFilters
from graphiti_core.utils.maintenance.graph_data_operations import clear_data
def make_serializable(data: Any) -> Any:
    """Recursively convert data to be JSON serializable."""
    if isinstance(data, (str, int, float, bool, type(None))):
        return data
    if isinstance(data, dict):
        return {k: make_serializable(v) for k, v in data.items()}
    if isinstance(data, list):
        return [make_serializable(i) for i in data]
    # For any other type, convert to its string representation.
    # This handles Pydantic models, datetime objects, etc.
    return str(data)

load_dotenv()
# If using Gemini, set a dummy OpenAI API key to prevent OpenAIRerankerClient from crashing on init.
# This is a workaround because Graphiti core unconditionally initializes the reranker.
if os.environ.get('LLM_PROVIDER', 'openai').lower() == 'gemini':
    os.environ.setdefault('OPENAI_API_KEY', 'dummy-key-for-init')


DEFAULT_LLM_MODEL = 'gpt-4.1-mini'
SMALL_LLM_MODEL = 'gpt-4.1-nano'
DEFAULT_EMBEDDER_MODEL = 'text-embedding-3-small'

# Semaphore limit for concurrent Graphiti operations.
# Decrease this if you're experiencing 429 rate limit errors from your LLM provider.
# Increase if you have high rate limits.
SEMAPHORE_LIMIT = int(os.getenv('SEMAPHORE_LIMIT', 10))


class NoOpEmbedder(EmbedderClient):
    """A dummy embedder that does nothing, but conforms to the EmbedderClient interface."""

    def __init__(self, dimension: int = 1536):
        self.dimension = dimension

    def embed(self, text: str) -> dict:
        """Returns a zero vector of the expected dimension."""
        return {"text": text, "vector": [0.0] * self.dimension}

    async def aembed_query(self, query: str) -> List[float]:
        """Asynchronously returns a zero vector for a query."""
        await asyncio.sleep(0)
        return [0.0] * self.dimension

    async def aembed_documents(self, documents: List[str]) -> List[List[float]]:
        """Asynchronously returns a list of zero vectors for a list of documents."""
        await asyncio.sleep(0)
        return [[0.0] * self.dimension for _ in documents]

    @classmethod
    def create(cls, **kwargs):
        """Creates an instance of the NoOpEmbedder."""
        # text-embedding-3-small has a dimension of 1536
        return cls(dimension=1536)


class VectorServiceEmbedder(EmbedderClient):
   """An embedder that uses an external vector generation service."""
   def __init__(self, config: "VectorServiceConfig"):
       self.config = config

   async def aembed_query(self, query: str) -> List[float]:
       """Generates a dense vector for a query."""
       response = await generate_embeddings(input=query)
       if isinstance(response, dict) and 'error' in response:
           raise Exception(f"Failed to embed query: {response['error']}")
       
       embedding_data = response.get("data", [{}])[0]
       dense_vector = embedding_data.get("embedding")
       if not dense_vector:
           raise Exception("No dense vector found in embedding service response.")
       return dense_vector

   async def aembed_documents(self, documents: List[str]) -> List[List[float]]:
       """Generates dense vectors for a list of documents."""
       # This can be optimized to batch requests in the future
       if not documents:
           return []
       
       # For now, we assume a single document is passed for simplicity in this flow.
       # A robust implementation would handle batching to the vector service.
       response = await generate_embeddings(input=documents[0])
       if isinstance(response, dict) and 'error' in response:
           raise Exception(f"Failed to embed document: {response['error']}")
       
       embedding_data = response.get("data", [{}])[0]
       dense_vector = embedding_data.get("embedding")
       if not dense_vector:
           raise Exception("No dense vector found in embedding service response for documents.")
       
       # Since this method is called for each document, we return a list containing the single vector.
       return [dense_vector]

   @classmethod
   def create(cls, **kwargs):
       """Creates an instance of the VectorServiceEmbedder."""
       return cls(config=kwargs.get("config"))

class Requirement(BaseModel):
    """A Requirement represents a specific need, feature, or functionality that a product or service must fulfill.

    Always ensure an edge is created between the requirement and the project it belongs to, and clearly indicate on the
    edge that the requirement is a requirement.

    Instructions for identifying and extracting requirements:
    1. Look for explicit statements of needs or necessities ("We need X", "X is required", "X must have Y")
    2. Identify functional specifications that describe what the system should do
    3. Pay attention to non-functional requirements like performance, security, or usability criteria
    4. Extract constraints or limitations that must be adhered to
    5. Focus on clear, specific, and measurable requirements rather than vague wishes
    6. Capture the priority or importance if mentioned ("critical", "high priority", etc.)
    7. Include any dependencies between requirements when explicitly stated
    8. Preserve the original intent and scope of the requirement
    9. Categorize requirements appropriately based on their domain or function
    """

    project_name: str = Field(
        ...,
        description='The name of the project to which the requirement belongs.',
    )
    description: str = Field(
        ...,
        description='Description of the requirement. Only use information mentioned in the context to write this description.',
    )


class Preference(BaseModel):
    """A Preference represents a user's expressed like, dislike, or preference for something.

    Instructions for identifying and extracting preferences:
    1. Look for explicit statements of preference such as "I like/love/enjoy/prefer X" or "I don't like/hate/dislike X"
    2. Pay attention to comparative statements ("I prefer X over Y")
    3. Consider the emotional tone when users mention certain topics
    4. Extract only preferences that are clearly expressed, not assumptions
    5. Categorize the preference appropriately based on its domain (food, music, brands, etc.)
    6. Include relevant qualifiers (e.g., "likes spicy food" rather than just "likes food")
    7. Only extract preferences directly stated by the user, not preferences of others they mention
    8. Provide a concise but specific description that captures the nature of the preference
    """

    category: str = Field(
        ...,
        description="The category of the preference. (e.g., 'Brands', 'Food', 'Music')",
    )
    description: str = Field(
        ...,
        description='Brief description of the preference. Only use information mentioned in the context to write this description.',
    )


class Procedure(BaseModel):
    """A Procedure informing the agent what actions to take or how to perform in certain scenarios. Procedures are typically composed of several steps.

    Instructions for identifying and extracting procedures:
    1. Look for sequential instructions or steps ("First do X, then do Y")
    2. Identify explicit directives or commands ("Always do X when Y happens")
    3. Pay attention to conditional statements ("If X occurs, then do Y")
    4. Extract procedures that have clear beginning and end points
    5. Focus on actionable instructions rather than general information
    6. Preserve the original sequence and dependencies between steps
    7. Include any specified conditions or triggers for the procedure
    8. Capture any stated purpose or goal of the procedure
    9. Summarize complex procedures while maintaining critical details
    """

    description: str = Field(
        ...,
        description='Brief description of the procedure. Only use information mentioned in the context to write this description.',
    )


class Architecture(BaseModel):
    """An Architecture node represents a component, pattern, or decision related to system architecture.
    
    Instructions for identifying and extracting architecture information:
    1. Look for descriptions of system components (e.g., 'API Gateway', 'User Service').
    2. Identify architectural patterns (e.g., 'Microservices', 'Event-Driven').
    3. Capture design decisions and their rationale.
    4. Note relationships and dependencies between components.
    """
    description: str = Field(
        ...,
        description='A brief description of the architectural component or concept.',
    )

class BugSolution(BaseModel):
    """A BugSolution node documents a specific bug and its resolution.
    
    Instructions for identifying and extracting bug solutions:
    1. Identify the problem or bug description.
    2. Capture the steps taken to diagnose the issue.
    3. Document the final solution or workaround.
    4. Note any related code changes, configuration updates, or error messages.
    """
    problem_summary: str = Field(
        ...,
        description='A summary of the bug or problem.',
    )
    solution_description: str = Field(
        ...,
        description='A description of the implemented solution.',
    )


ENTITY_TYPES: dict[str, BaseModel] = {
    'Requirement': Requirement,  # type: ignore
    'Preference': Preference,  # type: ignore
    'Procedure': Procedure,  # type: ignore
    'Architecture': Architecture,
    'BugSolution': BugSolution,
}


# Type definitions for API responses
class ErrorResponse(TypedDict):
    error: str


class SuccessResponse(TypedDict):
    message: str


class NodeResult(TypedDict):
    uuid: str
    name: str
    summary: str
    labels: list[str]
    group_id: str
    created_at: str
    attributes: dict[str, Any]


class NodeSearchResponse(TypedDict):
    message: str
    nodes: list[NodeResult]


class FactSearchResponse(TypedDict):
    message: str
    facts: list[dict[str, Any]]


class EpisodeSearchResponse(TypedDict):
    message: str
    episodes: list[dict[str, Any]]


class StatusResponse(TypedDict):
    status: str
    message: str


def create_azure_credential_token_provider() -> Callable[[], str]:
    credential = DefaultAzureCredential()
    token_provider = get_bearer_token_provider(
        credential, 'https://cognitiveservices.azure.com/.default'
    )
    return token_provider


# Server configuration classes
# The configuration system has a hierarchy:
# - GraphitiConfig is the top-level configuration
#   - LLMConfig handles all OpenAI/LLM related settings
#   - EmbedderConfig manages embedding settings
#   - Neo4jConfig manages database connection details
#   - Various other settings like group_id and feature flags
# Configuration values are loaded from:
# 1. Default values in the class definitions
# 2. Environment variables (loaded via load_dotenv())
# 3. Command line arguments (which override environment variables)
class GraphitiLLMConfig(BaseModel):
    """Configuration for the LLM client.

    Centralizes all LLM-specific configuration parameters including API keys and model selection.
    """

    provider: str = 'openai'
    api_key: str | None = None
    model: str = DEFAULT_LLM_MODEL
    small_model: str = SMALL_LLM_MODEL
    temperature: float = 0.0
    azure_openai_endpoint: str | None = None
    azure_openai_deployment_name: str | None = None
    azure_openai_api_version: str | None = None
    azure_openai_use_managed_identity: bool = False
    gemini_base_url: str | None = None

    @classmethod
    def from_env(cls) -> 'GraphitiLLMConfig':
        """Create LLM configuration from environment variables."""
        provider = os.environ.get('LLM_PROVIDER', 'openai').lower()

        # Get model from environment, or use default if not set or empty
        model_env = os.environ.get('MODEL_NAME', '')
        
        if provider == 'gemini':
            # For Gemini, use its specific default if MODEL_NAME is not set
            default_model = 'gemini-2.5-flash-preview-05-20'
            model = model_env if model_env.strip() else default_model
            gemini_base_url = os.environ.get('GEMINI_BASE_URL')
            return cls(
                provider=provider,
                api_key=os.environ.get('GOOGLE_API_KEY'),
                model=model,
                gemini_base_url=gemini_base_url,
            )

        # For other providers, use the general default
        model = model_env if model_env.strip() else DEFAULT_LLM_MODEL

        # Get small_model from environment, or use default if not set or empty
        small_model_env = os.environ.get('SMALL_MODEL_NAME', '')
        small_model = small_model_env if small_model_env.strip() else SMALL_LLM_MODEL

        azure_openai_endpoint = os.environ.get('AZURE_OPENAI_ENDPOINT', None)
        azure_openai_api_version = os.environ.get('AZURE_OPENAI_API_VERSION', None)
        azure_openai_deployment_name = os.environ.get('AZURE_OPENAI_DEPLOYMENT_NAME', None)
        azure_openai_use_managed_identity = (
            os.environ.get('AZURE_OPENAI_USE_MANAGED_IDENTITY', 'false').lower() == 'true'
        )

        if azure_openai_endpoint is None:
            # Setup for OpenAI API
            # Log if empty model was provided
            if model_env == '':
                logger.debug(
                    f'MODEL_NAME environment variable not set, using default: {DEFAULT_LLM_MODEL}'
                )
            elif not model_env.strip():
                logger.warning(
                    f'Empty MODEL_NAME environment variable, using default: {DEFAULT_LLM_MODEL}'
                )

            return cls(
                provider='openai',
                api_key=os.environ.get('OPENAI_API_KEY'),
                model=model,
                small_model=small_model,
                temperature=float(os.environ.get('LLM_TEMPERATURE', '0.0')),
            )
        else:
            # Setup for Azure OpenAI API
            # Log if empty deployment name was provided
            if azure_openai_deployment_name is None:
                logger.error('AZURE_OPENAI_DEPLOYMENT_NAME environment variable not set')

                raise ValueError('AZURE_OPENAI_DEPLOYMENT_NAME environment variable not set')
            if not azure_openai_use_managed_identity:
                # api key
                api_key = os.environ.get('OPENAI_API_KEY', None)
            else:
                # Managed identity
                api_key = None

            return cls(
                provider='azure',
                azure_openai_use_managed_identity=azure_openai_use_managed_identity,
                azure_openai_endpoint=azure_openai_endpoint,
                api_key=api_key,
                azure_openai_api_version=azure_openai_api_version,
                azure_openai_deployment_name=azure_openai_deployment_name,
                model=model,
                small_model=small_model,
                temperature=float(os.environ.get('LLM_TEMPERATURE', '0.0')),
            )

    @classmethod
    def from_cli_and_env(cls, args: argparse.Namespace) -> 'GraphitiLLMConfig':
        """Create LLM configuration from CLI arguments, falling back to environment variables."""
        # Start with environment-based config
        config = cls.from_env()

        # CLI arguments override environment variables when provided
        if hasattr(args, 'model') and args.model:
            # Only use CLI model if it's not empty
            if args.model.strip():
                config.model = args.model
            else:
                # Log that empty model was provided and default is used
                logger.warning(f'Empty model name provided, using default: {DEFAULT_LLM_MODEL}')

        if hasattr(args, 'small_model') and args.small_model:
            if args.small_model.strip():
                config.small_model = args.small_model
            else:
                logger.warning(f'Empty small_model name provided, using default: {SMALL_LLM_MODEL}')

        if hasattr(args, 'temperature') and args.temperature is not None:
            config.temperature = args.temperature

        return config

    def create_client(self) -> LLMClient:
        """Create an LLM client based on this configuration.

        Returns:
            LLMClient instance
        """

        if self.provider == 'gemini':
            if not self.api_key:
                raise ValueError('GOOGLE_API_KEY must be set when using Gemini')
            llm_config = LLMConfig(api_key=self.api_key, model=self.model)
            return GeminiLLMClient(config=llm_config, base_url=self.gemini_base_url)

        if self.provider == 'azure':
            # Azure OpenAI API setup
            if self.azure_openai_use_managed_identity:
                # Use managed identity for authentication
                token_provider = create_azure_credential_token_provider()
                return AzureOpenAILLMClient(
                    azure_client=AsyncAzureOpenAI(
                        azure_endpoint=self.azure_openai_endpoint,
                        azure_deployment=self.azure_openai_deployment_name,
                        api_version=self.azure_openai_api_version,
                        azure_ad_token_provider=token_provider,
                    ),
                    config=LLMConfig(
                        api_key=self.api_key,
                        model=self.model,
                        small_model=self.small_model,
                        temperature=self.temperature,
                    ),
                )
            elif self.api_key:
                # Use API key for authentication
                return AzureOpenAILLMClient(
                    azure_client=AsyncAzureOpenAI(
                        azure_endpoint=self.azure_openai_endpoint,
                        azure_deployment=self.azure_openai_deployment_name,
                        api_version=self.azure_openai_api_version,
                        api_key=self.api_key,
                    ),
                    config=LLMConfig(
                        api_key=self.api_key,
                        model=self.model,
                        small_model=self.small_model,
                        temperature=self.temperature,
                    ),
                )
            else:
                raise ValueError('OPENAI_API_KEY must be set when using Azure OpenAI API')

        # Default to OpenAI
        if not self.api_key:
            raise ValueError('OPENAI_API_KEY must be set when using OpenAI API')

        llm_client_config = LLMConfig(
            api_key=self.api_key, model=self.model, small_model=self.small_model
        )

        # Set temperature
        llm_client_config.temperature = self.temperature

        return OpenAIClient(config=llm_client_config)


class GraphitiEmbedderConfig(BaseModel):
    """Configuration for the embedder client.

    Centralizes all embedding-related configuration parameters.
    """

    model: str = DEFAULT_EMBEDDER_MODEL
    api_key: str | None = None
    azure_openai_endpoint: str | None = None
    azure_openai_deployment_name: str | None = None
    azure_openai_api_version: str | None = None
    azure_openai_use_managed_identity: bool = False

    @classmethod
    def from_env(cls) -> 'GraphitiEmbedderConfig':
        """Create embedder configuration from environment variables."""

        # Get model from environment, or use default if not set or empty
        model_env = os.environ.get('EMBEDDER_MODEL_NAME', '')
        model = model_env if model_env.strip() else DEFAULT_EMBEDDER_MODEL

        azure_openai_endpoint = os.environ.get('AZURE_OPENAI_EMBEDDING_ENDPOINT', None)
        azure_openai_api_version = os.environ.get('AZURE_OPENAI_EMBEDDING_API_VERSION', None)
        azure_openai_deployment_name = os.environ.get(
            'AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME', None
        )
        azure_openai_use_managed_identity = (
            os.environ.get('AZURE_OPENAI_USE_MANAGED_IDENTITY', 'false').lower() == 'true'
        )
        if azure_openai_endpoint is not None:
            # Setup for Azure OpenAI API
            # Log if empty deployment name was provided
            azure_openai_deployment_name = os.environ.get(
                'AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME', None
            )
            if azure_openai_deployment_name is None:
                logger.error('AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME environment variable not set')

                raise ValueError(
                    'AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME environment variable not set'
                )

            if not azure_openai_use_managed_identity:
                # api key
                api_key = os.environ.get('AZURE_OPENAI_EMBEDDING_API_KEY', None) or os.environ.get(
                    'OPENAI_API_KEY', None
                )
            else:
                # Managed identity
                api_key = None

            return cls(
                azure_openai_use_managed_identity=azure_openai_use_managed_identity,
                azure_openai_endpoint=azure_openai_endpoint,
                api_key=api_key,
                azure_openai_api_version=azure_openai_api_version,
                azure_openai_deployment_name=azure_openai_deployment_name,
            )
        else:
            return cls(
                model=model,
                api_key=os.environ.get('OPENAI_API_KEY'),
            )

    def create_client(self) -> EmbedderClient | None:
        if self.azure_openai_endpoint is not None:
            # Azure OpenAI API setup
            if self.azure_openai_use_managed_identity:
                # Use managed identity for authentication
                token_provider = create_azure_credential_token_provider()
                return AzureOpenAIEmbedderClient(
                    azure_client=AsyncAzureOpenAI(
                        azure_endpoint=self.azure_openai_endpoint,
                        azure_deployment=self.azure_openai_deployment_name,
                        api_version=self.azure_openai_api_version,
                        azure_ad_token_provider=token_provider,
                    ),
                    model=self.model,
                )
            elif self.api_key:
                # Use API key for authentication
                return AzureOpenAIEmbedderClient(
                    azure_client=AsyncAzureOpenAI(
                        azure_endpoint=self.azure_openai_endpoint,
                        azure_deployment=self.azure_openai_deployment_name,
                        api_version=self.azure_openai_api_version,
                        api_key=self.api_key,
                    ),
                    model=self.model,
                )
            else:
                logger.error('OPENAI_API_KEY must be set when using Azure OpenAI API')
                return None
        else:
            # OpenAI API setup
            if not self.api_key:
                return None

            embedder_config = OpenAIEmbedderConfig(api_key=self.api_key, embedding_model=self.model)

            return OpenAIEmbedder(config=embedder_config)


class Neo4jConfig(BaseModel):
    """Configuration for Neo4j database connection."""

    uri: str = 'bolt://localhost:7687'
    user: str = 'neo4j'
    password: str = 'password'

    @classmethod
    def from_env(cls) -> 'Neo4jConfig':
        """Create Neo4j configuration from environment variables."""
        return cls(
            uri=os.environ.get('NEO4J_URI', 'bolt://localhost:7687'),
            user=os.environ.get('NEO4J_USER', 'neo4j'),
            password=os.environ.get('NEO4J_PASSWORD', 'password'),
        )


class VectorServiceConfig(BaseModel):
    """Configuration for the external vector generation service."""
    endpoint: str = "http://***********:8002/api/v1/embeddings"
    model: str = "bge-m3"
    embedding_types: List[str] = Field(default_factory=lambda: ["dense", "sparse"])
    search_fusion_weights: dict[str, float] = Field(default_factory=lambda: {"dense": 0.5, "text": 0.2, "sparse": 0.3})
    search_fusion_mode: str = "weighted"

    @classmethod
    def from_env(cls) -> 'VectorServiceConfig':
        """Create vector service configuration from environment variables."""
        embedding_types_str = os.environ.get('VECTOR_SERVICE_EMBEDDING_TYPES', "dense,sparse")
        embedding_types = [item.strip() for item in embedding_types_str.split(',')]
        
        # Load fusion weights from environment variable
        fusion_weights_str = os.environ.get('SEARCH_FUSION_WEIGHTS', '{"dense": 0.5, "text": 0.2, "sparse": 0.3}')
        try:
            fusion_weights = json.loads(fusion_weights_str)
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON for SEARCH_FUSION_WEIGHTS, using default. Value: {fusion_weights_str}")
            fusion_weights = {"dense": 0.5, "text": 0.2, "sparse": 0.3}

        return cls(
            endpoint=os.environ.get('VECTOR_SERVICE_ENDPOINT', "http://***********:8002/api/v1/embeddings"),
            model=os.environ.get('VECTOR_SERVICE_MODEL', "bge-m3"),
            embedding_types=embedding_types,
            search_fusion_weights=fusion_weights,
            search_fusion_mode=os.environ.get('SEARCH_FUSION_MODE', 'weighted').lower(),
        )

class GraphitiConfig(BaseModel):
    """Configuration for Graphiti client.

    Centralizes all configuration parameters for the Graphiti client.
    """

    llm: GraphitiLLMConfig = Field(default_factory=GraphitiLLMConfig)
    embedder: GraphitiEmbedderConfig = Field(default_factory=GraphitiEmbedderConfig)
    neo4j: Neo4jConfig = Field(default_factory=Neo4jConfig)
    vector_service: VectorServiceConfig = Field(default_factory=VectorServiceConfig)
    group_id: str | None = None
    use_custom_entities: bool = False
    destroy_graph: bool = False
    vector_dimension: int

    @classmethod
    def from_env(cls) -> 'GraphitiConfig':
        """Create a configuration instance from environment variables."""
        return cls(
            llm=GraphitiLLMConfig.from_env(),
            embedder=GraphitiEmbedderConfig.from_env(),
            neo4j=Neo4jConfig.from_env(),
            vector_service=VectorServiceConfig.from_env(),
            vector_dimension=int(os.environ.get('VECTOR_DIMENSION', '1024')),
        )

    @classmethod
    def from_cli_and_env(cls, args: argparse.Namespace) -> 'GraphitiConfig':
        """Create configuration from CLI arguments, falling back to environment variables."""
        # Start with environment configuration
        config = cls.from_env()

        # Apply CLI overrides
        if args.group_id:
            config.group_id = args.group_id
        else:
            config.group_id = 'default'

        config.use_custom_entities = args.use_custom_entities
        config.destroy_graph = args.destroy_graph
        if hasattr(args, 'vector_dimension') and args.vector_dimension:
            config.vector_dimension = args.vector_dimension
# HACK: If running in stdio mode for local testing, override Neo4j URI
        # This is necessary because the parent environment may have Docker-specific settings
        if hasattr(args, 'transport') and args.transport == 'stdio':
            logger.info("Running in stdio mode, overriding Neo4j config for local testing.")
            config.neo4j.uri = 'bolt://10.11.5.202:7687'
            config.neo4j.user = 'neo4j'
            config.neo4j.password = '111QQQqqq111'

        # Update LLM config using CLI args
        config.llm = GraphitiLLMConfig.from_cli_and_env(args)

        return config


class MCPConfig(BaseModel):
    """Configuration for MCP server."""

    transport: str = 'sse'  # Default to SSE transport

    @classmethod
    def from_cli(cls, args: argparse.Namespace) -> 'MCPConfig':
        """Create MCP configuration from CLI arguments."""
        return cls(transport=args.transport)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr,
)
logger = logging.getLogger(__name__)

# Create global config instance - will be properly initialized later
config: GraphitiConfig | None = None

# MCP server instructions
GRAPHITI_MCP_INSTRUCTIONS = """
Graphiti is a memory service for AI agents built on a knowledge graph. Graphiti performs well
with dynamic data such as user interactions, changing enterprise data, and external information.

Graphiti transforms information into a richly connected knowledge network, allowing you to 
capture relationships between concepts, entities, and information. The system organizes data as episodes 
(content snippets), nodes (entities), and facts (relationships between entities), creating a dynamic, 
queryable memory store that evolves with new information. Graphiti supports multiple data formats, including 
structured JSON data, enabling seamless integration with existing data pipelines and systems.

Facts contain temporal metadata, allowing you to track the time of creation and whether a fact is invalid 
(superseded by new information).

Key capabilities:
1. Add episodes (text, messages, or JSON) to the knowledge graph with the add_memory tool
2. Search for nodes (entities) in the graph using natural language queries with search_nodes
3. Find relevant facts (relationships between entities) with search_facts
4. Retrieve specific entity edges or episodes by UUID
5. Manage the knowledge graph with tools like delete_episode, delete_entity_edge, and clear_graph

The server connects to a database for persistent storage and uses language models for certain operations. 
Each piece of information is organized by group_id, allowing you to maintain separate knowledge domains.

When adding information, provide descriptive names and detailed content to improve search quality. 
When searching, use specific queries and consider filtering by group_id for more relevant results.

For optimal performance, ensure the database is properly configured and accessible, and valid 
API keys are provided for any language model operations.
"""

# MCP server instance
mcp = FastMCP(
    'Graphiti Agent Memory',
    instructions=GRAPHITI_MCP_INSTRUCTIONS
)

# Initialize Graphiti client
graphiti_client: Graphiti | None = None


async def initialize_graphiti(graphiti_config: GraphitiConfig):
    """Initialize the Graphiti client with the configured settings."""
    global graphiti_client

    try:
        # Create LLM client if possible
        llm_client = graphiti_config.llm.create_client()
        if not llm_client and graphiti_config.use_custom_entities:
            # If custom entities are enabled, we must have an LLM client
            raise ValueError('OPENAI_API_KEY must be set when custom entities are enabled')

        # Validate Neo4j configuration
        if not graphiti_config.neo4j.uri or not graphiti_config.neo4j.user or not graphiti_config.neo4j.password:
            raise ValueError('NEO4J_URI, NEO4J_USER, and NEO4J_PASSWORD must be set')

        embedder_client: EmbedderClient | None = None
        # Per the new architecture, we prioritize VectorServiceEmbedder if its endpoint is configured.
        if os.environ.get('VECTOR_SERVICE_ENDPOINT'):
            logger.info(f"Using VectorServiceEmbedder with endpoint: {graphiti_config.vector_service.endpoint}")
            embedder_client = VectorServiceEmbedder.create(config=graphiti_config.vector_service)
        elif graphiti_config.llm.provider == 'gemini':
            logger.info('Using NoOpEmbedder for Gemini provider as a fallback.')
            embedder_client = NoOpEmbedder.create()
        else:
            logger.info('Using default OpenAI embedder as a fallback.')
            embedder_client = graphiti_config.embedder.create_client()

        if not embedder_client:
            raise ValueError('Embedder client could not be created.')

        # Initialize Graphiti client
        graphiti_client = Graphiti(
            uri=graphiti_config.neo4j.uri,
            user=graphiti_config.neo4j.user,
            password=graphiti_config.neo4j.password,
            llm_client=llm_client,
            embedder=embedder_client,
            max_coroutines=SEMAPHORE_LIMIT,
        )

        # Destroy graph if requested
        if graphiti_config.destroy_graph:
            logger.info('Destroying graph...')
            await clear_data(graphiti_client.driver)

        # Initialize the graph database with Graphiti's indices
        if graphiti_client:
            await graphiti_client.build_indices_and_constraints()
            
            # Create custom full-text index for hybrid search
            async with graphiti_client.driver.session(database="neo4j") as session:
                try:
                    logger.info("Creating full-text search index 'node_fulltext_search'...")
                    await session.run("""
                        CREATE FULLTEXT INDEX node_fulltext_search IF NOT EXISTS
                        FOR (n:Node | EpisodicNode | Episodic)
                        ON EACH [n.name, n.content, n.summary]
                    """)
                    logger.info("Full-text index created or already exists.")
                except Exception as e:
                    logger.error(f"Failed to create full-text index: {e}")

                # Create vector index for dense vectors
                try:
                    logger.info("Attempting to create vector search index 'node_dense_vector_index' for Node and EpisodicNode labels...")
                    
                    # Correct syntax for creating index on multiple labels is to create them separately
                    # or use a different syntax if the Neo4j version supports it.
                    # For broad compatibility, we create them for Node, assuming EpisodicNode inherits Node.
                    # A more robust solution would be to create for both if needed.
                    index_query = f"""
                        CREATE VECTOR INDEX node_dense_vector_index IF NOT EXISTS
                        FOR (n:Node) ON (n.dense_vector)
                        OPTIONS {{ indexConfig: {{
                            `vector.dimensions`: {graphiti_config.vector_dimension},
                            `vector.similarity_function`: 'cosine'
                        }} }}
                    """
                    await session.run(index_query)

                    # Also create for EpisodicNode if it doesn't automatically get covered
                    index_query_episodic = f"""
                        CREATE VECTOR INDEX episodic_node_dense_vector_index IF NOT EXISTS
                        FOR (n:EpisodicNode) ON (n.dense_vector)
                        OPTIONS {{ indexConfig: {{
                            `vector.dimensions`: {graphiti_config.vector_dimension},
                            `vector.similarity_function`: 'cosine'
                        }} }}
                    """
                    await session.run(index_query_episodic)

                    # Also create for Episodic if it doesn't automatically get covered
                    index_query_episodic_base = f"""
                        CREATE VECTOR INDEX episodic_dense_vector_index IF NOT EXISTS
                        FOR (n:Episodic) ON (n.dense_vector)
                        OPTIONS {{ indexConfig: {{
                            `vector.dimensions`: {graphiti_config.vector_dimension},
                            `vector.similarity_function`: 'cosine'
                        }} }}
                    """
                    await session.run(index_query_episodic_base)

                    # Verify index creation
                    result = await session.run("SHOW INDEXES YIELD name WHERE name CONTAINS 'dense_vector_index'")
                    indices = [record["name"] async for record in result]
                    if 'node_dense_vector_index' in indices:
                        logger.info("Successfully created or verified vector search index 'node_dense_vector_index'.")
                    else:
                        logger.warning("Vector search index 'node_dense_vector_index' was not found after creation attempt.")
                    if 'episodic_node_dense_vector_index' in indices:
                        logger.info("Successfully created or verified vector search index 'episodic_node_dense_vector_index'.")
                    else:
                        logger.warning("Vector search index 'episodic_node_dense_vector_index' was not found after creation attempt.")

                except Exception as e:
                    logger.error(f"Failed to create or verify vector search index: {e}", exc_info=True)

                # Check for GDS library
                # try:
                #     logger.info("Checking for GDS library...")
                #     gds_check = await session.run("RETURN gds.version() IS NOT NULL AS gdsExists")
                #     gds_exists = (await gds_check.single())["gdsExists"]
                #     if not gds_exists:
                #         logger.warning("GDS library not found. Vector similarity search will not work.")
                #     else:
                #         logger.info("GDS library found.")
                # except Exception:
                #     logger.warning("Could not check for GDS library. Vector similarity search may not work.")

            logger.info('Graphiti client initialized successfully')
        else:
            raise ValueError("Graphiti client failed to initialize")

        # Log configuration details for transparency
        if llm_client:
            provider_name = "Gemini" if graphiti_config.llm.provider == 'gemini' else "OpenAI"
            logger.info(f'Using {provider_name} model: {graphiti_config.llm.model}')
            logger.info(f'Using temperature: {graphiti_config.llm.temperature}')
        else:
            logger.info('No LLM client configured - entity extraction will be limited')

        logger.info(f'Using group_id: {graphiti_config.group_id}')
        logger.info(
            f'Custom entity extraction: {"enabled" if graphiti_config.use_custom_entities else "disabled"}'
        )
        logger.info(f'Using concurrency limit: {SEMAPHORE_LIMIT}')

    except Exception as e:
        logger.error(f'Failed to initialize Graphiti: {str(e)}')
        raise


def format_fact_result(edge: EntityEdge) -> dict[str, Any]:
    """Format an entity edge into a readable result.

    Since EntityEdge is a Pydantic BaseModel, we can use its built-in serialization capabilities.

    Args:
        edge: The EntityEdge to format

    Returns:
        A dictionary representation of the edge with serialized dates and excluded embeddings
    """
    result = edge.model_dump(
        mode='json',
        exclude={
            'fact_embedding',
        },
    )
    result.get('attributes', {}).pop('fact_embedding', None)
    return result


# Dictionary to store queues for each group_id
# Each queue is a list of tasks to be processed sequentially
@dataclass
class QueuedEpisode:
    """A dataclass to hold episode information for the processing queue."""
    name: str
    episode_body: str | dict
    source_type: EpisodeType
    group_id: str
    source_description: str
    uuid: str | None
    entity_types: dict
    reference_time: datetime
    labels: Optional[List[str]] = None


episode_queues: dict[str, asyncio.Queue[QueuedEpisode]] = {}
# Dictionary to track if a worker is running for each group_id
queue_workers: dict[str, asyncio.Task] = {}


async def process_episode_queue(group_id: str):
    """Process episodes for a specific group_id sequentially.

    This function runs as a long-lived task that processes episodes
    from the queue one at a time.
    """
    global queue_workers, graphiti_client

    logger.info(f'<<<<< Starting episode queue worker for group_id: {group_id} >>>>>')

    try:
        while True:
            logger.info(f"Worker for group '{group_id}' waiting for an episode...")
            # Get the next episode from the queue
            item = await episode_queues[group_id].get()
            logger.info(f"Worker for group '{group_id}' got an episode to process: {item.name}")

            try:
                if graphiti_client:
                    client = cast(Graphiti, graphiti_client)
                    
                    # 1. Create the EpisodicNode instance
                    new_episode = EpisodicNode(
                        name=item.name,
                        content=item.episode_body if isinstance(item.episode_body, str) else json.dumps(item.episode_body),
                        group_id=item.group_id,
                        source=item.source_type.value,
                        source_description=item.source_description,
                        uuid=item.uuid,
                        created_at=item.reference_time,
                        valid_at=item.reference_time,
                        labels=item.labels or [],
                    )
                    
                    # 2. Save the new node to the database FIRST to ensure it exists
                    await new_episode.create(client.driver)
                    logger.info(f"Successfully created and saved episode node '{item.name}' for group '{group_id}'.")

                    # 3. Now process the content of the EXISTING node
                    await client.process_episode_content(new_episode, item.entity_types)
                    logger.info(f"Successfully processed content for episode '{item.name}'.")

                    # 4. Now, generate vectors for the newly created/updated nodes
                    logger.info(f"Generating vectors for nodes related to episode '{item.name}' (UUID: {item.uuid})")
                    async with client.driver.session(database="neo4j") as session:
                        # Find the episodic node and all nodes it contains
                        query = """
                        MATCH (ep:EpisodicNode {uuid: $episode_uuid})
                        // Using OPTIONAL MATCH with a variable-length path to include the episodic node itself
                        // and all nodes connected via :CONTAINS relationship, directly or indirectly.
                        OPTIONAL MATCH (ep)-[:CONTAINS*0..]->(n:Node)
                        RETURN DISTINCT n
                        """
                        result = await session.run(query, {"episode_uuid": item.uuid})
                        nodes_to_process = [record["n"] async for record in result if record["n"] is not None]
                        
                        if nodes_to_process:
                            processed, updated = await _generate_and_update_vectors_for_nodes(nodes_to_process, force_update=True)
                            logger.info(f"Vector generation for episode '{item.name}' complete. Processed: {processed} nodes, Updated: {updated} nodes.")
                        else:
                            logger.warning(f"No nodes found to process for episode '{item.name}' (UUID: {item.uuid})")
                else:
                    logger.error(f"Graphiti client not available, cannot process episode '{item.name}'.")

            except Exception as e:
                logger.error(f"Error processing queued episode '{item.name}' for group_id {group_id}: {str(e)}", exc_info=True)
            finally:
                # Mark the task as done regardless of success/failure
                episode_queues[group_id].task_done()
                logger.info(f"Worker for group '{group_id}' finished processing an episode.")
    except asyncio.CancelledError:
        logger.info(f'Episode queue worker for group_id {group_id} was cancelled.')
    except Exception as e:
        logger.error(f'Unexpected error in queue worker for group_id {group_id}: {str(e)}', exc_info=True)
    finally:
        # Remove the worker task from the dictionary upon completion or cancellation
        if group_id in queue_workers:
            del queue_workers[group_id]
        logger.info(f'Stopped episode queue worker for group_id: {group_id}')


async def generate_embeddings(input: str) -> dict[str, Any] | ErrorResponse:
    """Generate dense and sparse embeddings for a given text using an external service."""
    try:
        async with httpx.AsyncClient() as client:
            payload = {
                "input": input,
                "model": config.vector_service.model,
                "return_dense": "dense" in config.vector_service.embedding_types,
                "return_sparse": "sparse" in config.vector_service.embedding_types,
            }
            response = await client.post(
                config.vector_service.endpoint,
                json=payload,
                timeout=30.0,  # Set a reasonable timeout
            )
            response.raise_for_status()  # Raise an exception for bad status codes
            return response.json()
    except httpx.RequestError as e:
        logger.error(f"HTTP request error while generating embeddings: {e}")
        return ErrorResponse(error=f"Failed to connect to vector service: {e}")
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP status error while generating embeddings: {e.response.status_code} - {e.response.text}")
        return ErrorResponse(error=f"Vector service returned an error: {e.response.status_code}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during embedding generation: {e}")
        return ErrorResponse(error=f"An unexpected error occurred: {e}")
@mcp.tool()
async def add_memory(
    name: str,
    episode_body: str | dict,
    group_id: str | None = None,
    source: str = 'text',
    source_description: str = '',
    uuid: str | None = None,
    labels: Optional[List[str]] = None,
) -> SuccessResponse | ErrorResponse:
    """Add an episode to memory. This is the primary way to add information to the graph.

    This function returns immediately and processes the episode addition in the background.
    Episodes for the same group_id are processed sequentially to avoid race conditions.

    Args:
        name (str): Name of the episode
        episode_body (str): The content of the episode to persist to memory. When source='json', this must be a
                           properly escaped JSON string, not a raw Python dictionary. The JSON data will be
                           automatically processed to extract entities and relationships.
        group_id (str, optional): A unique ID for this graph. If not provided, uses the default group_id from CLI
                                 or a generated one.
        source (str, optional): Source type, must be one of:
                               - 'text': For plain text content (default)
                               - 'json': For structured data
                               - 'message': For conversation-style content
        source_description (str, optional): Description of the source
        uuid (str, optional): Optional UUID for the episode
        labels (list[str], optional): A list of labels to apply to the node for categorization.

    Examples:
        # Adding plain text content with labels
        add_memory(
            name="API Gateway Architecture",
            episode_body="The API Gateway handles all incoming requests and routes them to the appropriate microservice.",
            source="text",
            labels=["Architecture", "Networking"]
        )

        # Adding plain text content
        add_memory(
            name="Company News",
            episode_body="Acme Corp announced a new product line today.",
            source="text",
            source_description="news article",
            group_id="some_arbitrary_string"
        )

        # Adding structured JSON data
        # NOTE: episode_body must be a properly escaped JSON string. Note the triple backslashes
        add_memory(
            name="Customer Profile",
            episode_body="{\\\"company\\\": {\\\"name\\\": \\\"Acme Technologies\\\"}, \\\"products\\\": [{\\\"id\\\": \\\"P001\\\", \\\"name\\\": \\\"CloudSync\\\"}, {\\\"id\\\": \\\"P002\\\", \\\"name\\\": \\\"DataMiner\\\"}]}",
            source="json",
            source_description="CRM data"
        )

        # Adding message-style content
        add_memory(
            name="Customer Conversation",
            episode_body="user: What's your return policy?\nassistant: You can return items within 30 days.",
            source="message",
            source_description="chat transcript",
            group_id="some_arbitrary_string"
        )

    Notes:
        When using source='json':
        - The JSON must be a properly escaped JSON string, not a raw Python dictionary
        - The JSON will be automatically processed to extract entities and relationships
        - Complex nested structures are supported (arrays, nested objects, mixed data types), but keep nesting to a minimum
        - Entities will be created from appropriate JSON properties
        - Relationships between entities will be established based on the JSON structure
    """
    global graphiti_client, episode_queues, queue_workers

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        # If source is JSON and body is a dict, convert it to a JSON string.
        # This handles cases where the request body is automatically parsed.
        if source.lower() == 'json':
            if isinstance(episode_body, dict):
                episode_body = json.dumps(episode_body)
            elif not isinstance(episode_body, str):
                return ErrorResponse(
                    error='episode_body must be a string or a JSON-serializable dict for source "json"'
                )
        elif not isinstance(episode_body, str):
            return ErrorResponse(error=f'episode_body must be a string for source "{source}"')

        # Map string source to EpisodeType enum
        source_type = EpisodeType.text
        if source.lower() == 'message':
            source_type = EpisodeType.message
        elif source.lower() == 'json':
            source_type = EpisodeType.json

        # Use the provided group_id or fall back to the default from config
        effective_group_id = group_id if group_id is not None else config.group_id if config else "default"
        group_id_str = str(effective_group_id) if effective_group_id is not None else ''

        episode_uuid = uuid if uuid is not None else str(uuid_lib.uuid4())

        # Prepare the episode item for the queue
        entity_types = ENTITY_TYPES if config.use_custom_entities else {}
        queued_episode = QueuedEpisode(
            name=name,
            episode_body=episode_body,
            source_type=source_type,
            group_id=group_id_str,
            source_description=source_description,
            uuid=episode_uuid,
            entity_types=entity_types,
            reference_time=datetime.now(timezone.utc),
            labels=labels,
        )

        # Initialize queue for this group_id if it doesn't exist
        if group_id_str not in episode_queues:
            episode_queues[group_id_str] = asyncio.Queue()
            logger.info(f"Created new episode queue for group_id: {group_id_str}")

        # Add the episode to the queue
        await episode_queues[group_id_str].put(queued_episode)
        logger.info(f"Queued episode '{name}' for group_id '{group_id_str}'. Queue size: {episode_queues[group_id_str].qsize()}")

        # Start a worker for this queue if one isn't already running or has finished.
        if group_id_str not in queue_workers or queue_workers[group_id_str].done():
            logger.info(f'Starting worker for group_id: {group_id_str}')
            task = asyncio.create_task(process_episode_queue(group_id_str))
            queue_workers[group_id_str] = task
        else:
            logger.info(f"Worker for group_id '{group_id_str}' is already running.")

        # Return immediately with a success message
        return SuccessResponse(
            message=f"Episode '{name}' queued for processing (position: {episode_queues[group_id_str].qsize()})"
        )
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error queuing episode: {error_msg}', exc_info=True)
        return ErrorResponse(error=f'Error queuing episode: {error_msg}')


@mcp.tool()
async def search_memory_nodes(
    query: str,
    group_ids: list[str] | None = None,
    max_nodes: int = 10,
    center_node_uuid: str | None = None,
    labels: Optional[List[str]] = None,
) -> NodeSearchResponse | ErrorResponse:
    """Search the graph memory for relevant node summaries.
    These contain a summary of all of a node's relationships with other nodes.

    Args:
        query: The search query
        group_ids: Optional list of group IDs to filter results
        max_nodes: Maximum number of nodes to return (default: 10)
        center_node_uuid: Optional UUID of a node to center the search around
        labels: Optional list of labels to filter the nodes by.
    """
    global graphiti_client

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        effective_group_ids = (
            group_ids if group_ids is not None else [config.group_id] if config and config.group_id else []
        )
        client = cast(Graphiti, graphiti_client)

        embedding_response = await generate_embeddings(input=query)
        if isinstance(embedding_response, dict) and 'error' in embedding_response:
            return ErrorResponse(error=f"Failed to generate query embeddings: {embedding_response.get('error')}")

        embedding_data = embedding_response.get("data")
        result_data = None
        if isinstance(embedding_data, list) and embedding_data:
            result_data = embedding_data[0]
        elif isinstance(embedding_data, dict):
            result_data = embedding_data

        if not result_data:
            return ErrorResponse(error="Invalid embedding data received from vector service.")

        query_dense_vector = result_data.get("embedding")
        raw_sparse_vector = result_data.get("sparse_embedding", {})
        
        query_sparse_map = {}
        if raw_sparse_vector and 'indices' in raw_sparse_vector and 'values' in raw_sparse_vector:
            query_sparse_map = {
                str(raw_sparse_vector['indices'][i]): raw_sparse_vector['values'][i]
                for i in range(len(raw_sparse_vector['indices']))
            }

        if not query_dense_vector:
            return ErrorResponse(error="Could not generate dense vector for the query.")

        params = {
            "query": query,
            "group_ids": effective_group_ids,
            "labels": labels,
            "query_dense_vector": query_dense_vector,
            "query_sparse_vector": query_sparse_map,
            "limit": max_nodes
        }

        if config.vector_service.search_fusion_mode == 'rrf':
            cypher_query = """
            // RRF Hybrid Search
            CALL {
                CALL db.index.vector.queryNodes('node_dense_vector_index', $limit, $query_dense_vector) YIELD node, score
                RETURN node, score, 'dense' as type
                UNION ALL
                CALL db.index.vector.queryNodes('episodic_node_dense_vector_index', $limit, $query_dense_vector) YIELD node, score
                RETURN node, score, 'dense' as type
                UNION ALL
                CALL db.index.vector.queryNodes('episodic_dense_vector_index', $limit, $query_dense_vector) YIELD node, score
                RETURN node, score, 'dense' as type
            }
            WITH node, score, type
            WITH node, score, type ORDER BY score DESC
            WITH collect({node: node, score: score, type: type}) AS dense_results
            CALL {
                CALL db.index.fulltext.queryNodes('node_fulltext_search', $query, {limit: $limit}) YIELD node, score
                RETURN node, score, 'text' as type
            }
            WITH dense_results, node, score, type
            WITH dense_results, collect({node: node, score: score, type: type}) AS text_results
            CALL {
                MATCH (node)
                WHERE node.sparse_vector_indices IS NOT NULL AND node.sparse_vector_values IS NOT NULL
                WITH node, reduce(
                    sparseDotProduct = 0.0,
                    i IN range(0, size(node.sparse_vector_indices) - 1) |
                    sparseDotProduct + (coalesce($query_sparse_vector[toString(node.sparse_vector_indices[i])], 0.0) * node.sparse_vector_values[i])
                ) AS score
                WHERE score > 0
                RETURN node, score, 'sparse' as type
            }
            WITH dense_results, text_results, collect({node: node, score: score, type: type}) AS sparse_results
            WITH dense_results + text_results + sparse_results AS all_results
            UNWIND all_results AS r
            WITH r.node as node, r.score as score, r.type as type
            WHERE ($group_ids IS NULL OR node.group_id IN $group_ids) AND ($labels IS NULL OR size([l IN $labels WHERE l IN labels(node)]) > 0)
            WITH node, type, score
            ORDER BY score DESC
            WITH node, type, collect(score) as scores
            WITH node, type, scores, range(0, size(scores)) as ranks
            UNWIND ranks as rank
            WITH node, sum(1.0 / (60 + rank + 1)) as totalScore
            ORDER BY totalScore DESC
            LIMIT $limit
            RETURN node, totalScore
            """
        else: # Default to weighted
            cypher_query = """
            // Weighted Hybrid Search
            CALL () {
                CALL () {
                    CALL db.index.vector.queryNodes('node_dense_vector_index', $limit, $query_dense_vector) YIELD node, score
                    RETURN node, score, 'dense' AS type
                    UNION ALL
                    CALL db.index.vector.queryNodes('episodic_node_dense_vector_index', $limit, $query_dense_vector) YIELD node, score
                    RETURN node, score, 'dense' AS type
                    UNION ALL
                    CALL db.index.vector.queryNodes('episodic_dense_vector_index', $limit, $query_dense_vector) YIELD node, score
                    RETURN node, score, 'dense' AS type
                }
                RETURN collect({node: node, score: score, type: type}) AS results
            }
            WITH results AS dense_results
            CALL () {
                CALL db.index.fulltext.queryNodes('node_fulltext_search', $query, {limit: $limit}) YIELD node, score
                RETURN collect({node: node, score: score, type: 'text'}) AS text_results
            }
            WITH dense_results, text_results
            WITH dense_results + text_results AS all_results
            UNWIND all_results AS result
            WITH result.node AS node, result.type AS type, result.score AS score
            WHERE ($group_ids IS NULL OR node.group_id IN $group_ids)
              AND ($labels IS NULL OR size([label IN $labels WHERE label IN labels(node)]) > 0)
            WITH node, collect({type: type, score: score}) AS scores_list
            WITH node, scores_list,
                 coalesce(head([s in scores_list WHERE s.type = 'dense' | s.score]), 0.0) AS denseScore,
                 coalesce(head([s in scores_list WHERE s.type = 'text' | s.score]), 0.0) AS textScore,
                 CASE
                   WHEN node.sparse_vector_indices IS NOT NULL AND node.sparse_vector_values IS NOT NULL
                   THEN reduce(
                     sparseDotProduct = 0.0,
                     i IN range(0, size(node.sparse_vector_indices) - 1) |
                     sparseDotProduct + (coalesce($query_sparse_vector[toString(node.sparse_vector_indices[i])], 0.0) * node.sparse_vector_values[i])
                   )
                   ELSE 0.0
                 END AS sparseScore
            WITH node, denseScore, textScore, sparseScore,
                 ($dense_weight * denseScore) + ($text_weight * textScore) + ($sparse_weight * sparseScore) AS totalScore
            WHERE totalScore > 0
            ORDER BY totalScore DESC
            LIMIT $limit
            RETURN node, totalScore
            """
            params.update({
                "dense_weight": config.vector_service.search_fusion_weights.get("dense", 0.5),
                "text_weight": config.vector_service.search_fusion_weights.get("text", 0.2),
                "sparse_weight": config.vector_service.search_fusion_weights.get("sparse", 0.3)
            })

        async with client.driver.session(database="neo4j") as session:
            result = await session.run(cypher_query, params)
            search_results = await result.data()

        if not search_results:
            return NodeSearchResponse(message='No relevant nodes found', nodes=[])

        formatted_nodes: list[NodeResult] = []
        for record in search_results:
            node_data = record['node']
            score = record['totalScore']
            
            created_at_val = node_data.get('created_at')
            if hasattr(created_at_val, 'to_native'):
                created_at_iso = created_at_val.to_native().isoformat()
            else:
                created_at_iso = str(created_at_val)

            attributes = dict(node_data.get('attributes', {}))
            attributes['search_score'] = score

            formatted_nodes.append({
                'uuid': node_data.get('uuid'),
                'name': node_data.get('name'),
                'summary': node_data.get('summary') or attributes.get('summary', ''),
                'labels': list(node_data.get('labels', [])),
                'group_id': node_data.get('group_id'),
                'created_at': created_at_iso,
                'attributes': attributes,
            })

        return NodeSearchResponse(message='Nodes retrieved successfully', nodes=formatted_nodes)
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error searching nodes: {error_msg}')
        return ErrorResponse(error=f'Error searching nodes: {error_msg}')


@mcp.tool()
async def search_memory_facts(
    query: str,
    group_ids: list[str] | None = None,
    max_facts: int = 10,
    center_node_uuid: str | None = None,
) -> FactSearchResponse | ErrorResponse:
    """Search the graph memory for relevant facts.

    Args:
        query: The search query
        group_ids: Optional list of group IDs to filter results
        max_facts: Maximum number of facts to return (default: 10)
        center_node_uuid: Optional UUID of a node to center the search around
    """
    global graphiti_client

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        # Validate max_facts parameter
        if max_facts <= 0:
            return ErrorResponse(error='max_facts must be a positive integer')

        # Use the provided group_ids or fall back to the default from config if none provided
        effective_group_ids = (
            group_ids if group_ids is not None else [config.group_id] if config and config.group_id else []
        )

        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # If using NoOpEmbedder, vector search is not possible. Return empty list.
        if isinstance(client.embedder, NoOpEmbedder):
            logger.info("Skipping fact search because NoOpEmbedder is configured.")
            return FactSearchResponse(message='Vector search not available with NoOpEmbedder', facts=[])

        try:
            relevant_edges = await client.search(
                group_ids=effective_group_ids,
                query=query,
                num_results=max_facts,
                center_node_uuid=center_node_uuid,
            )
        except TypeError as e:
            if "can't be used in 'await' expression" in str(e):
                logger.warning(f"Caught a TypeError in search, returning empty list: {e}")
                return FactSearchResponse(message='Search failed due to TypeError', facts=[])
            raise e

        if not relevant_edges:
            return FactSearchResponse(message='No relevant facts found', facts=[])

        facts = [format_fact_result(edge) for edge in relevant_edges]
        return FactSearchResponse(message='Facts retrieved successfully', facts=facts)
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error searching facts: {error_msg}')
        return ErrorResponse(error=f'Error searching facts: {error_msg}')


@mcp.tool()
async def delete_entity_edge(uuid: str) -> SuccessResponse | ErrorResponse:
    """Delete an entity edge from the graph memory.

    Args:
        uuid: UUID of the entity edge to delete
    """
    global graphiti_client

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Get the entity edge by UUID
        entity_edge = await EntityEdge.get_by_uuid(client.driver, uuid)
        # Delete the edge using its delete method
        await entity_edge.delete(client.driver)
        return SuccessResponse(message=f'Entity edge with UUID {uuid} deleted successfully')
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error deleting entity edge: {error_msg}')
        return ErrorResponse(error=f'Error deleting entity edge: {error_msg}')


@mcp.tool()
async def delete_episode(uuid: str) -> SuccessResponse | ErrorResponse:
    """Delete an episode from the graph memory.

    Args:
        uuid: UUID of the episode to delete
    """
    global graphiti_client

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Get the episodic node by UUID - EpisodicNode is already imported at the top
        episodic_node = await EpisodicNode.get_by_uuid(client.driver, uuid)
        # Delete the node using its delete method
        await episodic_node.delete(client.driver)
        return SuccessResponse(message=f'Episode with UUID {uuid} deleted successfully')
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error deleting episode: {error_msg}')
        return ErrorResponse(error=f'Error deleting episode: {error_msg}')


@mcp.tool()
async def get_entity_edge(uuid: str) -> dict[str, Any] | ErrorResponse:
    """Get an entity edge from the graph memory by its UUID.

    Args:
        uuid: UUID of the entity edge to retrieve
    """
    global graphiti_client

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Get the entity edge directly using the EntityEdge class method
        entity_edge = await EntityEdge.get_by_uuid(client.driver, uuid)

        # Use the format_fact_result function to serialize the edge
        # Return the Python dict directly - MCP will handle serialization
        return format_fact_result(entity_edge)
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error getting entity edge: {error_msg}')
        return ErrorResponse(error=f'Error getting entity edge: {error_msg}')


@mcp.tool()
async def get_episodes(
    group_id: str | None = None, last_n: int = 10
) -> EpisodeSearchResponse | ErrorResponse:
    """Get the most recent memory episodes for a specific group.

    Args:
        group_id: ID of the group to retrieve episodes from. If not provided, uses the default group_id.
        last_n: Number of most recent episodes to retrieve (default: 10)
    """
    global graphiti_client

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        # Use the provided group_id or fall back to the default from config
        effective_group_id = group_id if group_id is not None else config.group_id if config else "default"

        if not isinstance(effective_group_id, str):
            return ErrorResponse(error='Group ID must be a string')

        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        episodes = await client.retrieve_episodes(
            group_ids=[effective_group_id], last_n=last_n, reference_time=datetime.now(timezone.utc)
        )

        if not episodes:
            return EpisodeSearchResponse(
                message=f'No episodes found for group {effective_group_id}', episodes=[]
            )

        # Use Pydantic's model_dump method for EpisodicNode serialization
        formatted_episodes = [
            # Use mode='json' to handle datetime serialization
            episode.model_dump(mode='json')
            for episode in episodes
        ]

        # Return the Python list directly - MCP will handle serialization
        return EpisodeSearchResponse(
            message=f'{len(formatted_episodes)} episodes retrieved successfully',
            episodes=formatted_episodes,
        )
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error getting episodes: {error_msg}')
        return ErrorResponse(error=f'Error getting episodes: {error_msg}')


@mcp.tool()
async def clear_graph() -> SuccessResponse | ErrorResponse:
    """Clear all data from the graph memory and rebuild indices."""
    global graphiti_client

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Use a more robust DETACH DELETE to ensure all nodes and relationships are removed.
        async with client.driver.session(database="neo4j") as session:
            await session.run("MATCH (n) DETACH DELETE n")
        await client.build_indices_and_constraints()
        return SuccessResponse(message='Graph cleared successfully and indices rebuilt')
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error clearing graph: {error_msg}')
        return ErrorResponse(error=f'Error clearing graph: {error_msg}')


@mcp.tool()
async def get_all_nodes_and_facts(group_id: str | None = None) -> dict[str, Any] | ErrorResponse:
    """Get all nodes and facts (entity edges) directly from the database for a given group_id."""
    global graphiti_client

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        client = cast(Graphiti, graphiti_client)
        effective_group_id = group_id if group_id is not None else config.group_id if config else "default"
        if not effective_group_id:
            return ErrorResponse(error='Group ID must be provided')

        # Cypher query to get all nodes and relationships for the group
        query = """
        MATCH (n) WHERE n.group_id = $group_id
        OPTIONAL MATCH (n)-[r]->(m) WHERE m.group_id = $group_id
        RETURN n, r, m
        """
        params = {"group_id": effective_group_id}
        
        nodes = []
        facts = []
        
        # Using the async context manager for the session
        async with client.driver.session(database="neo4j") as session:
            result = await session.run(query, params)
            
            # Process the results
            async for record in result:
                node_data = record.get("n")
                if node_data:
                    nodes.append(dict(node_data))

                rel_data = record.get("r")
                if rel_data:
                    # Create a dictionary from the relationship properties
                    fact_info = {
                        "uuid": rel_data.element_id,  # Use element_id for relationship identifier
                        "type": rel_data.type,
                        "properties": dict(rel_data),
                        "start_node": dict(record.get("n")),
                        "end_node": dict(record.get("m"))
                    }
                    facts.append(fact_info)

        # Deduplicate nodes and facts
        unique_nodes = {node['uuid']: node for node in nodes}.values()
        unique_facts = {fact['uuid']: fact for fact in facts}.values()

        return {
            "message": f"Retrieved {len(unique_nodes)} nodes and {len(unique_facts)} facts.",
            "nodes": list(unique_nodes),
            "facts": list(unique_facts)
        }
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error getting all nodes and facts: {error_msg}')
        return ErrorResponse(error=f'Error getting all nodes and facts: {error_msg}')


async def _generate_and_update_vectors_for_nodes(
    nodes_to_process: list[dict], force_update: bool = False
) -> tuple[int, int]:
    """
    Helper function to generate and update embeddings for a list of nodes.

    Args:
        nodes_to_process (list[dict]): A list of node data dictionaries from Neo4j.
        force_update (bool, optional): If True, regenerate embeddings even for nodes that already have them.

    Returns:
        tuple[int, int]: A tuple containing the number of nodes processed and updated.
    """
    global graphiti_client, config
    if not graphiti_client:
        logger.error("Graphiti client not available for vector generation.")
        return 0, 0

    nodes_processed = 0
    nodes_updated = 0
    client = cast(Graphiti, graphiti_client)

    async with client.driver.session(database="neo4j") as session:
        for node_data in nodes_to_process:
            nodes_processed += 1
            
            node_uuid = node_data.get('uuid')
            # Use 'name' for embedding, fallback to 'content' or 'summary'
            text_to_embed = node_data.get('name') or node_data.get('content') or node_data.get('summary')
            
            # Skip if no text to embed
            if not text_to_embed:
                logger.warning(f"Skipping vector generation for node {node_uuid} as it has no name, content, or summary.")
                continue

            # Check if we should skip this node
            if not force_update and 'dense_vector' in node_data and node_data['dense_vector']:
                continue

            logger.info(f"Generating embeddings for node '{text_to_embed[:50]}...' (UUID: {node_uuid})")
            embedding_response = await generate_embeddings(input=text_to_embed)

            if not (isinstance(embedding_response, dict) and 'error' in embedding_response):
                embedding_data = embedding_response.get("data", [{}])[0]
                dense_vector = embedding_data.get("embedding")
                sparse_embedding = embedding_data.get("sparse_embedding", {})
                sparse_vector_indices = sparse_embedding.get("indices")
                sparse_vector_values = sparse_embedding.get("values")

                if dense_vector:
                    update_query = """
                    MATCH (n {uuid: $uuid})
                    SET n.dense_vector = $dense_vector,
                        n.sparse_vector_indices = $sparse_vector_indices,
                        n.sparse_vector_values = $sparse_vector_values
                    """
                    await session.run(update_query, {
                        "uuid": node_uuid,
                        "dense_vector": dense_vector,
                        "sparse_vector_indices": sparse_vector_indices,
                        "sparse_vector_values": sparse_vector_values
                    })
                    nodes_updated += 1
                    logger.info(f"Successfully updated embeddings for node UUID: {node_uuid}")
                else:
                    logger.error(f"No valid embedding data in response for node UUID: {node_uuid}")
            else:
                error_msg = embedding_response.get('error', 'Unknown error')
                logger.error(f"Failed to generate embeddings for node UUID {node_uuid}: {error_msg}")
    
    return nodes_processed, nodes_updated


@mcp.tool()
async def regenerate_all_node_vectors(group_id: str | None = None, force_update: bool = False) -> SuccessResponse | ErrorResponse:
    """
    Iterate through all nodes in a given group_id and generate embeddings for them.

    This tool is useful for backfilling vector embeddings for nodes created before
    the vector generation feature was implemented.

    Args:
        group_id (str, optional): The ID of the group to process. If not provided, uses the default group_id.
        force_update (bool, optional): If True, will regenerate embeddings even for nodes that already have them. Defaults to False.
    """
    global graphiti_client, config

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        client = cast(Graphiti, graphiti_client)
        effective_group_id = group_id if group_id is not None else config.group_id if config else "default"
        if not effective_group_id:
            return ErrorResponse(error='Group ID must be provided')

        logger.info(f"Starting vector regeneration for group_id: {effective_group_id}")

        # Cypher query to get all nodes for the group
        query = """
        MATCH (n) WHERE n.group_id = $group_id
        RETURN n
        """
        params = {"group_id": effective_group_id}
        
        nodes_to_process = []
        async with client.driver.session(database="neo4j") as session:
            result = await session.run(query, params)
            nodes_to_process = [record["n"] async for record in result]

        if not nodes_to_process:
            logger.info(f"No nodes found in group '{effective_group_id}' to regenerate.")
            return SuccessResponse(message=f"No nodes found in group '{effective_group_id}'.")

        nodes_processed, nodes_updated = await _generate_and_update_vectors_for_nodes(nodes_to_process, force_update=force_update)

        message = f"Vector regeneration complete for group '{effective_group_id}'. Processed: {nodes_processed} nodes, Updated: {nodes_updated} nodes."
        logger.info(message)
        return SuccessResponse(message=message)

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error during vector regeneration: {error_msg}', exc_info=True)
        return ErrorResponse(error=f'Error during vector regeneration: {error_msg}')


@mcp.tool()
async def rebuild_indices() -> SuccessResponse | ErrorResponse:
    """Drop all known search indices and rebuild them. Useful if index definitions change."""
    global graphiti_client
    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        async with graphiti_client.driver.session(database="neo4j") as session:
            logger.info("Dropping existing search indices...")
            await session.run("DROP INDEX node_dense_vector_index IF EXISTS")
            await session.run("DROP INDEX episodic_node_dense_vector_index IF EXISTS")
            await session.run("DROP INDEX episodic_dense_vector_index IF EXISTS")
            await session.run("DROP INDEX node_fulltext_search IF EXISTS")
            logger.info("Indices dropped successfully.")

            logger.info("Rebuilding indices...")
            # Re-run the index creation logic from initialize_graphiti
            await graphiti_client.build_indices_and_constraints()
            
            logger.info("Creating full-text search index 'node_fulltext_search'...")
            await session.run("""
                CREATE FULLTEXT INDEX node_fulltext_search IF NOT EXISTS
                FOR (n:Node | EpisodicNode)
                ON EACH [n.name, n.content, n.summary]
            """)
            
            logger.info("Creating vector search index 'node_dense_vector_index'...")
            logger.info("Creating vector search index 'node_dense_vector_index'...")
            await session.run(f"""
                CREATE VECTOR INDEX node_dense_vector_index IF NOT EXISTS
                FOR (n:Node) ON (n.dense_vector)
                OPTIONS {{ indexConfig: {{
                    `vector.dimensions`: {config.vector_dimension},
                    `vector.similarity_function`: 'cosine'
                }} }}
            """)

            logger.info("Creating vector search index 'episodic_node_dense_vector_index'...")
            await session.run(f"""
                CREATE VECTOR INDEX episodic_node_dense_vector_index IF NOT EXISTS
                FOR (n:EpisodicNode) ON (n.dense_vector)
                OPTIONS {{ indexConfig: {{
                    `vector.dimensions`: {config.vector_dimension},
                    `vector.similarity_function`: 'cosine'
                }} }}
            """)

            logger.info("Creating vector search index 'episodic_dense_vector_index'...")
            await session.run(f"""
                CREATE VECTOR INDEX episodic_dense_vector_index IF NOT EXISTS
                FOR (n:Episodic) ON (n.dense_vector)
                OPTIONS {{ indexConfig: {{
                    `vector.dimensions`: {config.vector_dimension},
                    `vector.similarity_function`: 'cosine'
                }} }}
            """)
            logger.info("All indices rebuilt successfully.")
            
        return SuccessResponse(message="Search indices rebuilt successfully.")
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error rebuilding indices: {error_msg}', exc_info=True)
        return ErrorResponse(error=f'Error rebuilding indices: {error_msg}')


@mcp.resource('http://graphiti/status')
async def get_status() -> StatusResponse:
    """Get the status of the Graphiti MCP server and Neo4j connection."""
    global graphiti_client

    if graphiti_client is None:
        return StatusResponse(status='error', message='Graphiti client not initialized')

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Test database connection
        await client.driver.client.verify_connectivity()  # type: ignore

        return StatusResponse(
            status='ok', message='Graphiti MCP server is running and connected to Neo4j'
        )
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error checking Neo4j connection: {error_msg}')
        return StatusResponse(
            status='error',
            message=f'Graphiti MCP server is running but Neo4j connection failed: {error_msg}',
        )


# _process_json_episode has been removed as it was part of the manual vector handling
# which is now delegated to graphiti-core.


async def initialize_server(cli_args: List[str] | None = None) -> MCPConfig:
    """Parse CLI arguments and initialize the Graphiti server configuration."""
    global config
    load_dotenv()

    parser = argparse.ArgumentParser(
        description='Run the Graphiti MCP server with optional LLM client'
    )
    parser.add_argument(
        '--group-id',
        help='Namespace for the graph. This is an arbitrary string used to organize related data. '
        'If not provided, a random UUID will be generated.',
    )
    parser.add_argument(
        '--transport',
        choices=['sse', 'stdio'],
        default='sse',
        help='Transport to use for communication with the client. (default: sse)',
    )
    parser.add_argument(
        '--model', help=f'Model name to use with the LLM client. (default: {DEFAULT_LLM_MODEL})'
    )
    parser.add_argument(
        '--small-model',
        help=f'Small model name to use with the LLM client. (default: {SMALL_LLM_MODEL})',
    )
    parser.add_argument(
        '--temperature',
        type=float,
        help='Temperature setting for the LLM (0.0-2.0). Lower values make output more deterministic. (default: 0.7)',
    )
    parser.add_argument(
        '--vector-dimension',
        type=int,
        default=None,
        help='Dimension of the vectors for indexing. Overrides VECTOR_DIMENSION from .env.',
    )
    parser.add_argument('--destroy-graph', action='store_true', help='Destroy all Graphiti graphs')
    parser.add_argument(
        '--use-custom-entities',
        action='store_true',
        help='Enable entity extraction using the predefined ENTITY_TYPES',
    )
    parser.add_argument(
        '--host',
        default=os.environ.get('MCP_SERVER_HOST', '0.0.0.0'),
        help='Host to bind the MCP server to (default: 127.0.0.1)',
    )
    parser.add_argument(
        '--port',
        type=int,
        default=os.environ.get('MCP_SERVER_PORT', 8002),
        help='Port to bind the MCP server to (default: 8002)',
    )

    args = parser.parse_args(cli_args)

    # Build configuration from CLI arguments and environment variables
    config = GraphitiConfig.from_cli_and_env(args)

    # Log the group ID configuration
    if args.group_id:
        logger.info(f'Using provided group_id: {config.group_id}')
    else:
        logger.info(f'Generated random group_id: {config.group_id}')

    # Log entity extraction configuration
    if config.use_custom_entities:
        logger.info('Entity extraction enabled using predefined ENTITY_TYPES')
    else:
        logger.info('Entity extraction disabled (no custom entities will be used)')

    if args.host:
        logger.info(f'Setting MCP server host to: {args.host}')
        # Set MCP server host from CLI or env
        mcp.settings.host = args.host
    if args.port:
        logger.info(f'Setting MCP server port to: {args.port}')
        mcp.settings.port = args.port

    # Return MCP configuration
    return MCPConfig.from_cli(args)


async def run_mcp_server(mcp_config: MCPConfig):
    """Run the MCP server in the current event loop."""
    # Run the server with stdio transport for MCP in the same event loop
    logger.info(f'Starting MCP server with transport: {mcp_config.transport}')
    if mcp_config.transport == 'stdio':
        # Disable uvicorn's access logger for stdio to prevent polluting the communication channel
        logging.getLogger("uvicorn.access").disabled = True
        await mcp.run_stdio_async()
    elif mcp_config.transport == 'sse':
        logger.info(
            f'Running MCP server with SSE transport on {mcp.settings.host}:{mcp.settings.port}'
        )
        
        # Get the existing SSE app from FastMCP
        app = mcp.sse_app()

        # Define a startup event to pre-start the default worker
        async def startup_event():
            """Initialize Graphiti and pre-start a worker for the default group_id."""
            await initialize_graphiti(config)
            default_group_id = str(config.group_id) if config else "default"
            if default_group_id not in episode_queues:
                episode_queues[default_group_id] = asyncio.Queue()
            if default_group_id not in queue_workers or queue_workers[default_group_id].done():
                logger.info(f"Pre-starting worker for default group_id: '{default_group_id}'")
                task = asyncio.create_task(process_episode_queue(default_group_id))
                queue_workers[default_group_id] = task

        app.add_event_handler("startup", startup_event)

        # The FastMCP sse_app is expected to handle the JSON-RPC endpoint automatically.
        # The custom handle_rpc function was causing errors and has been removed.

        async def get_node_details(request: Request):
            """Get details for a specific node by its UUID."""
            uuid = request.path_params['uuid']
            if not graphiti_client:
                return Response(content=json.dumps({"error": "Graphiti client not initialized"}), status_code=500)
            try:
                node = await EpisodicNode.get_by_uuid(graphiti_client.driver, uuid)
                if not node:
                    return Response(content=json.dumps({"error": "Node not found"}), status_code=404)
                
                # Use Pydantic's model_dump for serialization
                # Use Pydantic's model_dump for serialization
                node_dict = node.model_dump()
                # Manually add dense_vector if it exists
                if hasattr(node, 'dense_vector'):
                    node_dict['dense_vector'] = node.dense_vector
                return Response(content=json.dumps(make_serializable(node_dict)), media_type="application/json")
            except Exception as e:
                logger.error(f"Error getting node {uuid}: {e}", exc_info=True)
                return Response(content=json.dumps({"error": str(e)}), status_code=500)

        app.add_route("/v1/graph/nodes/{uuid}", get_node_details, methods=["GET"])

        # Manually run the modified app with Uvicorn
        uvicorn_config = uvicorn.Config(app, host=mcp.settings.host, port=mcp.settings.port, log_level="info")
        server = uvicorn.Server(uvicorn_config)
        await server.serve()


async def main(cli_args: List[str] | None = None):
    """Main function to run the Graphiti MCP server."""
    # Initialize the server and Graphiti
    mcp_config = await initialize_server(cli_args)
    # Graphiti is now initialized in the startup_event

    # Run the MCP server
    await run_mcp_server(mcp_config)


if __name__ == '__main__':
    asyncio.run(main())