import asyncio
import os
import pytest
from unittest.mock import patch, MagicMock, AsyncMock

# It's important to set environment variables before importing the server code
os.environ['NEO4J_URI'] = 'bolt://localhost:7687'
os.environ['NEO4J_USER'] = 'neo4j'
os.environ['NEO4J_PASSWORD'] = 'password'
os.environ['OPENAI_API_KEY'] = 'test-key' # needed for initialization

import graphiti_mcp_server
from graphiti_mcp_server import search_memory_nodes, initialize_graphiti, config, GraphitiConfig

@pytest.mark.asyncio
async def test_search_with_weighted_fusion():
    """Test search_memory_nodes with weighted fusion mode."""
    os.environ['SEARCH_FUSION_MODE'] = 'weighted'
    os.environ['SEARCH_FUSION_WEIGHTS'] = '{"dense": 0.7, "text": 0.1, "sparse": 0.2}'
    
    test_config = GraphitiConfig.from_env()

    with patch('graphiti_mcp_server.Graphiti') as Mock<PERSON><PERSON>hit<PERSON>, \
         patch('graphiti_mcp_server.generate_embeddings') as mock_gen_embeddings:

        mock_gen_embeddings.return_value = {"data": [{"embedding": [0.1] * 1024, "sparse_embedding": {"indices": [10, 20], "values": [0.5, 0.5]}}]}
        
        mock_session = MagicMock()
        mock_session.run = AsyncMock()
        mock_session.run.return_value.data.return_value = [{"node": {"uuid": "test-uuid"}, "totalScore": 0.99}]
        
        mock_driver = MagicMock()
        mock_driver.session.return_value.__aenter__.return_value = mock_session
        
        mock_graphiti_instance = MockGraphiti.return_value
        mock_graphiti_instance.driver = mock_driver
        graphiti_mcp_server.graphiti_client = mock_graphiti_instance

        with patch('graphiti_mcp_server.config', test_config):
            result = await search_memory_nodes(query="test query")
            
            mock_session.run.assert_called_once()
            call_args = mock_session.run.call_args
            cypher_query = call_args[0][0]
            params = call_args[0][1]

            assert "Weighted Hybrid Search" in cypher_query
            assert params['dense_weight'] == 0.7
            assert result['message'] == 'Nodes retrieved successfully'

        graphiti_mcp_server.graphiti_client = None

@pytest.mark.asyncio
async def test_search_with_rrf_fusion():
    """Test search_memory_nodes with RRF fusion mode."""
    os.environ['SEARCH_FUSION_MODE'] = 'rrf'
    
    test_config = GraphitiConfig.from_env()

    with patch('graphiti_mcp_server.Graphiti') as MockGraphiti, \
         patch('graphiti_mcp_server.generate_embeddings') as mock_gen_embeddings:

        mock_gen_embeddings.return_value = {"data": [{"embedding": [0.1] * 1024, "sparse_embedding": {"indices": [10, 20], "values": [0.5, 0.5]}}]}
        
        mock_session = MagicMock()
        mock_session.run = AsyncMock()
        mock_session.run.return_value.data.return_value = [{"node": {"uuid": "test-uuid"}, "totalScore": 0.99}]
        
        mock_driver = MagicMock()
        mock_driver.session.return_value.__aenter__.return_value = mock_session
        
        mock_graphiti_instance = MockGraphiti.return_value
        mock_graphiti_instance.driver = mock_driver
        graphiti_mcp_server.graphiti_client = mock_graphiti_instance

        with patch('graphiti_mcp_server.config', test_config):
            result = await search_memory_nodes(query="test query")

            mock_session.run.assert_called_once()
            call_args = mock_session.run.call_args
            cypher_query = call_args[0][0]

            assert "RRF Hybrid Search" in cypher_query
            assert result['message'] == 'Nodes retrieved successfully'
        
        graphiti_mcp_server.graphiti_client = None