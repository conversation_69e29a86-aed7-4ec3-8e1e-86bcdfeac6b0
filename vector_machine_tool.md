# Vector Manufacturing Machine Tool: `generate_embeddings`

This document provides instructions for using the `generate_embeddings` tool, which is integrated into the Graphiti MCP server.

## Overview

The `generate_embeddings` tool acts as a client to the "Vector Manufacturing Machine" service. It allows you to generate dense and sparse vector embeddings for a given text input by calling an external API endpoint.

## Tool Function: `generate_embeddings`

### Description

Generates dense and sparse vector embeddings for a given text input using the Vector Manufacturing Machine.

### Arguments

-   `input` (Union[str, List[str]]): The text or list of texts to embed. This is a required parameter.
-   `model` (str, optional): The model to use for embedding. Defaults to `'bge-m3'`.

### Returns

A dictionary containing the embedding data from the API or an `ErrorResponse` if the request fails.

### Example Usage

Here's how you can call the tool using the `use_mcp_tool` function:

```python
# Example of generating an embedding for a single string
<use_mcp_tool>
  <server_name>Graphiti</server_name>
  <tool_name>generate_embeddings</tool_name>
  <arguments>
    {
      "input": "This is a test sentence.",
      "model": "bge-m3"
    }
  </arguments>
</use_mcp_tool>

# Example of generating embeddings for a list of strings
<use_mcp_tool>
  <server_name>Graphiti</server_name>
  <tool_name>generate_embeddings</tool_name>
  <arguments>
    {
      "input": [
        "First sentence to embed.",
        "Second sentence to embed."
      ],
      "model": "bge-m3"
    }
  </arguments>
</use_mcp_tool>
```

## API Endpoint

The tool communicates with the following external API endpoint:

-   **URL**: `http://10.11.5.201:8002/api/v1/embeddings`
-   **Method**: `POST`

## Error Handling

If the API call fails (e.g., due to a network issue or an error from the vector service), the tool will return an `ErrorResponse` object containing details about the failure.

## Configuration

The connection details for the Vector Manufacturing Machine service can be configured via environment variables. Create a `.env` file in the root of the project and add the following variables:

```
# .env file

# The full URL to the embeddings endpoint
VECTOR_SERVICE_ENDPOINT="http://10.11.5.201:8002/api/v1/embeddings"

# The default model to use for generating embeddings
VECTOR_SERVICE_MODEL="bge-m3"
```

If these variables are not set, the tool will use the default values shown above.