import sys
import json
import uuid
import requests
import time
import os
from dotenv import load_dotenv

# The server URL for SSE testing
SERVER_URL = "http://10.11.5.202:8002/"

def send_request(method, params, request_id):
    """Helper function to send a JSON-RPC request via HTTP POST and get the response."""
    request = {
        "jsonrpc": "2.0",
        "method": method,
        "params": params,
        "id": request_id
    }
    headers = {'Content-Type': 'application/json'}
    
    try:
        print(f"Sending request for {method}...", file=sys.stderr)
        response = requests.post(SERVER_URL, json=request, headers=headers, timeout=60)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        
        response_json = response.json()
        print(f"Received for {method}: {json.dumps(response_json, indent=2)}", file=sys.stderr)
        return response_json

    except requests.exceptions.RequestException as e:
        print(f'\n--- Test failed for {method}: HTTP Request failed: {e} ---', file=sys.stderr)
        return None
    except json.JSONDecodeError:
        print(f'\n--- Test failed for {method}: Failed to decode JSON response: {response.text} ---', file=sys.stderr)
        return None


def main():
    """
    Test script for the Graphiti MCP server using SSE (HTTP).
    """
    # Load environment variables from .env file
    load_dotenv()
    # --- 1. Initial clear_graph to ensure a clean slate ---
    print('\n--- 1. Testing clear_graph (initial) ---', file=sys.stderr)
    clear_response = send_request('clear_graph', {}, 'clear_1')
    assert clear_response and 'result' in clear_response, "Initial clear_graph failed"
    print('--- clear_graph (initial) successful ---', file=sys.stderr)

    # --- 2. Add memory episodes ---
    print('\n--- 2. Testing add_memory ---', file=sys.stderr)
    # Add a text episode
    add_text_response = send_request(
        'add_memory',
        {'name': 'Test Text Episode', 'episode_body': 'This is a test of adding plain text via SSE.', 'source': 'text'},
        'add_text_1'
    )
    assert add_text_response and 'result' in add_text_response and 'error' not in add_text_response['result'], f"add_memory (text) failed: {add_text_response.get('result', {}).get('error')}"

    # Add a JSON episode
    json_body = {"user": {"name": "Kilo", "status": "testing_sse"}, "action": "add_memory"}
    add_json_response = send_request(
        'add_memory',
        {'name': 'Test JSON Episode', 'episode_body': json.dumps(json_body), 'source': 'json'},
        'add_json_1'
    )
    assert add_json_response and 'result' in add_json_response and 'error' not in add_json_response['result'], f"add_memory (json) failed: {add_json_response.get('result', {}).get('error')}"

    # Add a delay to allow the server to process the queued episodes
    print('\n--- Waiting 15 seconds for episode processing... ---', file=sys.stderr)
    time.sleep(15)

    # --- 3. Get episodes to verify addition ---
    print('\n--- 3. Testing get_episodes ---', file=sys.stderr)
    get_episodes_response = send_request('get_episodes', {'last_n': 5}, 'get_episodes_1')
    assert get_episodes_response and 'result' in get_episodes_response, "get_episodes failed"
    episodes = get_episodes_response['result']['episodes']
    print(f"--- Episodes received: {json.dumps(episodes, indent=2)} ---", file=sys.stderr)
    assert len(episodes) >= 2, "get_episodes should have returned at least 2 episodes"
    # We can no longer assert based on UUIDs, so we'll check names instead.
    names_in_response = [e['name'] for e in episodes]
    assert 'Test Text Episode' in names_in_response, "Text episode not found"
    assert 'Test JSON Episode' in names_in_response, "JSON episode not found"
    print('--- get_episodes test successful ---', file=sys.stderr)

    # --- 4. Search for nodes and facts ---
    print('\n--- 4. Testing search_memory_nodes and search_memory_facts ---', file=sys.stderr)
    
    llm_provider = os.environ.get('LLM_PROVIDER', 'openai').lower()

    if llm_provider == 'gemini':
        print('--- LLM_PROVIDER is gemini, expecting no vector search results ---', file=sys.stderr)
        search_nodes_response = send_request('search_memory_nodes', {'query': 'Kilo'}, 'search_nodes_1')
        assert search_nodes_response and 'result' in search_nodes_response, "search_memory_nodes failed"
        nodes = search_nodes_response['result']['nodes']
        assert len(nodes) == 0, "Search for 'Kilo' should return zero nodes with NoOpEmbedder"
        assert 'Vector search not available' in search_nodes_response['result']['message'], "Message should indicate vector search is unavailable"

        search_facts_response = send_request('search_memory_facts', {'query': 'plain text'}, 'search_facts_1')
        assert search_facts_response and 'result' in search_facts_response, "search_memory_facts failed"
        facts = search_facts_response['result']['facts']
        assert len(facts) == 0, "Search for 'plain text' should return zero facts with NoOpEmbedder"
        assert 'Vector search not available' in search_facts_response['result']['message'], "Message should indicate vector search is unavailable"
        
        print('--- Search tests successful for Gemini (NoOpEmbedder) ---', file=sys.stderr)
        # Since search returns no results, subsequent tests for get/delete edge will fail.
        # We can consider the test successful at this point for the Gemini case.
    else:
        print('--- LLM_PROVIDER is not gemini, expecting vector search results ---', file=sys.stderr)
        search_nodes_response = send_request('search_memory_nodes', {'query': 'Kilo'}, 'search_nodes_1')
        assert search_nodes_response and 'result' in search_nodes_response, "search_memory_nodes failed"
        nodes = search_nodes_response['result']['nodes']
        assert len(nodes) > 0, "Search for 'Kilo' should return at least one node"
        
        search_facts_response = send_request('search_memory_facts', {'query': 'plain text'}, 'search_facts_1')
        assert search_facts_response and 'result' in search_facts_response, "search_memory_facts failed"
        facts = search_facts_response['result']['facts']
        print(f"--- Facts received: {json.dumps(facts, indent=2)} ---", file=sys.stderr)
        assert len(facts) > 0, "Search for 'plain text' should return at least one fact"
        edge_to_get_uuid = facts[0]['uuid']
        print('--- Search tests successful ---', file=sys.stderr)

        # --- 5. Get a specific entity edge ---
        print('\n--- 5. Testing get_entity_edge ---', file=sys.stderr)
        get_edge_response = send_request('get_entity_edge', {'uuid': edge_to_get_uuid}, 'get_edge_1')
        assert get_edge_response and 'result' in get_edge_response, "get_entity_edge failed"
        assert get_edge_response['result']['uuid'] == edge_to_get_uuid, "get_entity_edge returned wrong edge"
        print('--- get_entity_edge test successful ---', file=sys.stderr)

        # --- 6. Delete an episode and an edge ---
        print('\n--- 6. Testing delete_episode and delete_entity_edge ---', file=sys.stderr)
        # We need a UUID to delete, so we'll get it from the first episode.
        episode_to_delete_uuid = episodes[0]['uuid']
        delete_episode_response = send_request('delete_episode', {'uuid': episode_to_delete_uuid}, 'delete_episode_1')
        assert delete_episode_response and 'result' in delete_episode_response, "delete_episode failed"
        
        delete_edge_response = send_request('delete_entity_edge', {'uuid': edge_to_get_uuid}, 'delete_edge_1')
        assert delete_edge_response and 'result' in delete_edge_response, "delete_entity_edge failed"
        print('--- Deletion tests successful ---', file=sys.stderr)

    # --- 7. Final clear_graph to clean up ---
    print('\n--- 7. Testing clear_graph (final) ---', file=sys.stderr)
    final_clear_response = send_request('clear_graph', {}, 'clear_final')
    assert final_clear_response and 'result' in final_clear_response, "Final clear_graph failed"
    print('--- clear_graph (final) successful ---', file=sys.stderr)

    print('\n\n--- ALL TESTS PASSED SUCCESSFULLY ---', file=sys.stderr)

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f'\n--- A test failed with an unhandled exception: {e} ---', file=sys.stderr)
        sys.exit(1)