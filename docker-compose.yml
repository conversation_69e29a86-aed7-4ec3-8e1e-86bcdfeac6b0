services:
  graphiti-mcp:
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - path: .env
        required: false # Makes the file optional. Default value is 'true'
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - MODEL_NAME=${MODEL_NAME}
      - PATH=/root/.local/bin:${PATH}
      - SEMAPHORE_LIMIT=${SEMAPHORE_LIMIT:-10}
      - LLM_PROVIDER=${LLM_PROVIDER}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GEMINI_BASE_URL=${GEMINI_BASE_URL}
      - VECTOR_DIMENSION=${VECTOR_DIMENSION}
    ports:
      - "0.0.0.0:8002:8002" # Expose the MCP server via HTTP for SSE transport
    command: ["uv", "run", "graphiti_mcp_server.py", "--transport", "sse", "--host", "0.0.0.0"]
    networks:
      - 1panel-network

networks:
  1panel-network:
    external: true
