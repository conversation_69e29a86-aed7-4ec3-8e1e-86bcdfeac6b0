更新import asyncio
import os
from dotenv import load_dotenv
import json

# This is a simplified client to interact with the MCP server
# In a real scenario, you would use a proper t library
async def call_mcp_tool(tool_name, arguments):
    import httpx
    # Assuming the server is running on the default port
    url = "http://127.0.0.1:8002/jsonrpc"
    headers = {"Content-Type": "application/json"}
    payload = {
        "jsonrpc": "2.0",
        "method": tool_name,
        "params": arguments,
        "id": 1,
    }
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.ConnectError as e:
            print(f"Connection failed: Could not connect to the MCP server at {url}.")
            print("Please ensure the server is running.")
            return {"error": {"message": "Connection failed"}}
        except Exception as e:
            print(f"An error occurred: {e}")
            return {"error": {"message": str(e)}}

async def main():
    """
    Main function to run the hybrid search test.
    """
    load_dotenv()

    # 1. Add some data to the graph to ensure we have something to search for.
    # Using a mix of technical terms to test the search capabilities.
    episodes_to_add = [
        {
            "name": "Dense Vector Search",
            "episode_body": "Dense vector search is a technique used in information retrieval to find similar items based on their vector representations in a high-dimensional space. It relies on models like word2vec or BERT.",
            "group_id": "default",
            "source": "text"
        },
        {
            "name": "Sparse Vector Search",
            "episode_body": "Sparse vector search, often using algorithms like BM25, is effective for keyword-based searches. It represents documents using high-dimensional but sparse vectors, where most elements are zero.",
            "group_id": "default",
            "source": "text"
        },
        {
            "name": "Hybrid Search",
            "episode_body": "Hybrid search combines the strengths of both dense and sparse vector search to provide more relevant results. It balances semantic understanding with keyword matching.",
            "group_id": "default",
            "source": "text"
        }
    ]

    print("Adding episodes to the graph...")
    for episode in episodes_to_add:
        add_result = await call_mcp_tool("add_memory", episode)
        print(f"Added '{episode['name']}': {add_result}")
        # Wait a bit for processing, especially if the queue is being used
        await asyncio.sleep(5) 
    
    print("\n--- Finished adding data ---\n")
    
    # 2. Perform the hybrid search
    search_query = "vector search techniques"
    print(f"Performing hybrid search for: '{search_query}'")

    search_args = {
        "query": search_query,
        "group_ids": ["default"],
        "max_nodes": 5
    }

    search_result = await call_mcp_tool("search_memory_nodes", search_args)

    print("\n--- Search Result ---")
    if "result" in search_result and search_result["result"].get("nodes"):
        nodes = search_result["result"]["nodes"]
        print(f"Found {len(nodes)} nodes:")
        for i, node in enumerate(nodes):
            print(f"  {i+1}. Name: {node.get('name')}")
            print(f"     UUID: {node.get('uuid')}")
            print(f"     Score: {node.get('attributes', {}).get('search_score')}")
            print(f"     Labels: {node.get('labels')}")
    elif "error" in search_result:
        print(f"An error occurred during search: {search_result['error']['message']}")
    else:
        print("No nodes found or an unexpected response was received.")
        print("Full response:", json.dumps(search_result, indent=2))

if __name__ == "__main__":
    asyncio.run(main())